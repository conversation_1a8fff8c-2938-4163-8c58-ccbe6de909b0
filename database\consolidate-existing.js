const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD || 'admin',
  port: process.env.DB_PORT || 5432,
});

async function consolidateExistingDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Checking current database state...');
    
    // Check current migration count
    const migrationCount = await client.query('SELECT COUNT(*) as count FROM migrations');
    console.log(`📊 Current migrations: ${migrationCount.rows[0].count}`);
    
    // Check if already consolidated
    const consolidationCheck = await client.query(
      "SELECT EXISTS(SELECT 1 FROM migrations WHERE filename = '001_consolidated_schema.sql') as is_consolidated"
    );
    
    if (consolidationCheck.rows[0].is_consolidated) {
      console.log('✅ Database is already consolidated!');
      return;
    }
    
    console.log('🔄 Marking database as consolidated...');
    
    // Mark as consolidated
    await client.query(`
      INSERT INTO migrations (filename, executed_at)
      VALUES ('001_consolidated_schema.sql', CURRENT_TIMESTAMP)
      ON CONFLICT DO NOTHING
    `);
    
    // Add to migration log if table exists
    try {
      await client.query(`
        INSERT INTO migration_log (migration_name, description)
        VALUES (
          'Schema Consolidation v1.0',
          'Existing database with 64 historical migrations marked as consolidated'
        )
      `);
    } catch (err) {
      console.log('ℹ️  Migration log table not found, skipping log entry');
    }
    
    console.log('✅ Database successfully marked as consolidated!');
    console.log('🚀 Your database is now ready for future migrations starting from 065');
    console.log('');
    console.log('Next steps:');
    console.log('1. Your existing database schema is preserved');
    console.log('2. All 64 historical migrations are marked as applied');
    console.log('3. Future migrations will start from 065');
    console.log('4. You can now use the consolidated migration system for new deployments');
    
  } catch (error) {
    console.error('❌ Error during consolidation:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

// Run the consolidation
consolidateExistingDatabase()
  .then(() => {
    console.log('🎉 Consolidation completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Consolidation failed:', error.message);
    process.exit(1);
  });