-- Migration 051: Create system_tasks and system_health_logs tables for task management system
-- This migration adds tables for tracking maintenance tasks and system health status

-- Create system_tasks table
CREATE TABLE IF NOT EXISTS system_tasks (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER, -- in seconds
    auto_executable BOOLEAN DEFAULT false,
    metadata JSONB,
    created_by INTEGER REFERENCES users(id)
);

-- Create system_health_logs table
CREATE TABLE IF NOT EXISTS system_health_logs (
    id SERIAL PRIMARY KEY,
    module VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('operational', 'warning', 'critical')),
    issues JSONB,
    metrics JSONB,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_system_tasks_type ON system_tasks(type);
CREATE INDEX IF NOT EXISTS idx_system_tasks_priority ON system_tasks(priority);
CREATE INDEX IF NOT EXISTS idx_system_tasks_status ON system_tasks(status);
CREATE INDEX IF NOT EXISTS idx_system_tasks_scheduled_for ON system_tasks(scheduled_for) WHERE scheduled_for IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_system_tasks_auto_executable ON system_tasks(auto_executable) WHERE auto_executable = true;
CREATE INDEX IF NOT EXISTS idx_system_tasks_created_at ON system_tasks(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_system_health_logs_module ON system_health_logs(module);
CREATE INDEX IF NOT EXISTS idx_system_health_logs_status ON system_health_logs(status);
CREATE INDEX IF NOT EXISTS idx_system_health_logs_checked_at ON system_health_logs(checked_at DESC);

-- Add comments for documentation
COMMENT ON TABLE system_tasks IS 'Maintenance tasks for system health management';
COMMENT ON COLUMN system_tasks.type IS 'Type of task (maintenance, cleanup, monitoring, optimization)';
COMMENT ON COLUMN system_tasks.priority IS 'Priority level of the task';
COMMENT ON COLUMN system_tasks.status IS 'Current status of the task';
COMMENT ON COLUMN system_tasks.title IS 'Short descriptive title of the task';
COMMENT ON COLUMN system_tasks.description IS 'Detailed description of the task';
COMMENT ON COLUMN system_tasks.scheduled_for IS 'When the task is scheduled to be executed';
COMMENT ON COLUMN system_tasks.completed_at IS 'When the task was completed';
COMMENT ON COLUMN system_tasks.estimated_duration IS 'Estimated duration in seconds';
COMMENT ON COLUMN system_tasks.auto_executable IS 'Whether the task can be executed automatically';
COMMENT ON COLUMN system_tasks.metadata IS 'Additional task-specific data in JSON format';
COMMENT ON COLUMN system_tasks.created_by IS 'User who created the task';

COMMENT ON TABLE system_health_logs IS 'Logs of system health status checks';
COMMENT ON COLUMN system_health_logs.module IS 'Module that was checked (shift_management, assignment_management, trip_monitoring)';
COMMENT ON COLUMN system_health_logs.status IS 'Health status of the module';
COMMENT ON COLUMN system_health_logs.issues IS 'Detailed information about detected issues in JSON format';
COMMENT ON COLUMN system_health_logs.metrics IS 'Performance metrics and other data in JSON format';
COMMENT ON COLUMN system_health_logs.checked_at IS 'When the health check was performed';

-- Insert initial test records to verify table structure
INSERT INTO system_tasks (
    type,
    priority,
    status,
    title,
    description,
    auto_executable
) VALUES (
    'system_initialization',
    'medium',
    'completed',
    'Initialize Task Management System',
    'Created system_tasks table for task management',
    false
);

INSERT INTO system_health_logs (
    module,
    status,
    issues,
    metrics
) VALUES (
    'system_initialization',
    'operational',
    '{"message": "System health monitoring initialized"}',
    '{"tables_created": ["system_tasks", "system_health_logs"]}'
);

-- Grant permissions to application user (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'hauling_app_user') THEN
        GRANT SELECT, INSERT, UPDATE ON system_tasks TO hauling_app_user;
        GRANT USAGE, SELECT ON SEQUENCE system_tasks_id_seq TO hauling_app_user;
        
        GRANT SELECT, INSERT, UPDATE ON system_health_logs TO hauling_app_user;
        GRANT USAGE, SELECT ON SEQUENCE system_health_logs_id_seq TO hauling_app_user;
    END IF;
END $$;