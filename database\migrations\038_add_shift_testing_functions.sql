-- Migration: Add comprehensive shift testing functions
-- Purpose: Validate shift logic for day and night shifts
-- Created: 2025-07-16

-- Function to test shift scenarios comprehensively
CREATE OR REPLACE FUNCTION test_shift_scenarios()
RETURNS TABLE (
    scenario TEXT,
    shift_type TEXT,
    start_time TIME,
    end_time TIME,
    test_time TIME,
    expected_status TEXT,
    actual_status TEXT,
    passed BOOLEAN
) AS $$
DECLARE
    test_shift_id INTEGER;
    test_date DATE := CURRENT_DATE;
BEGIN
    -- Test Day Shift Scenarios
    -- Scenario 1: Day shift before start
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift before start'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '07:00:00'::TIME,
        'scheduled'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '07:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '07:00:00'::TIME) = 'scheduled';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 2: Day shift during active period
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift during active'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '12:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '12:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '12:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 3: Day shift after end
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift after end'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '17:00:00'::TIME,
        'completed'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '17:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '17:00:00'::TIME) = 'completed';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Test Night Shift Scenarios
    -- Scenario 4: Night shift before start (same day)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift before start'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '20:00:00'::TIME,
        'scheduled'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '20:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '20:00:00'::TIME) = 'scheduled';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 5: Night shift during active (evening)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift active evening'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '23:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '23:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '23:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 6: Night shift during active (morning next day)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift active morning'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '03:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '03:00:00'::TIME),
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '03:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 7: Night shift completed (next day after end)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift completed'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '07:00:00'::TIME,
        'completed'::TEXT,
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '07:00:00'::TIME),
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '07:00:00'::TIME) = 'completed';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
END;
$$ LANGUAGE plpgsql;

-- Function to validate current shift statuses
CREATE OR REPLACE FUNCTION validate_all_shift_statuses()
RETURNS TABLE (
    shift_id INTEGER,
    truck_id INTEGER,
    driver_id INTEGER,
    shift_type TEXT,
    current_status TEXT,
    calculated_status TEXT,
    needs_update BOOLEAN,
    is_overnight BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.status::TEXT,
        evaluate_shift_status(ds.id, CURRENT_TIMESTAMP),
        (ds.status::TEXT != evaluate_shift_status(ds.id, CURRENT_TIMESTAMP)),
        (ds.end_time < ds.start_time)
    FROM driver_shifts ds
    WHERE ds.status != 'cancelled'
    ORDER BY ds.truck_id, ds.start_time;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION test_shift_scenarios() IS 
'Comprehensive testing function for day and night shift logic validation';

COMMENT ON FUNCTION validate_all_shift_statuses() IS 
'Validates all current shift statuses against calculated statuses';