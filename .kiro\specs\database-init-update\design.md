# Design Document

## Overview

This design document outlines the approach for updating the database initialization file (`init.sql`) to incorporate all migrations up to version 063. The updated file will serve as a consolidated reference for the complete database schema and will simplify the setup process for new environments. The design focuses on creating a well-organized, documented, and optimized initialization file that includes all recent features and fixes.

## Architecture

The database initialization file will follow a clear, modular structure that groups related database objects together. The file will be organized into logical sections, with each section containing related tables, functions, triggers, and other database objects. This approach will make the file easier to maintain and understand.

### File Structure

```
init.sql
├── Header and Documentation
├── Drop Existing Objects
├── Extensions
├── Enums and Custom Types
├── Core Tables
│   ├── Users
│   ├── Dump Trucks
│   ├── Drivers
│   ├── Locations
├── Operational Tables
│   ├── Assignments
│   ├── Trip Logs
│   ├── Approvals
│   ├── Scan Logs
├── Shift Management Tables
│   ├── Driver Shifts
│   ├── Shift Handovers
├── System Monitoring Tables
│   ├── System Logs
│   ├── System Tasks
│   ├── System Health Logs
├── Indexes
├── Functions and Procedures
├── Triggers
├── Views and Materialized Views
├── Initial Data Seeding
└── Migration Tracking
```

## Components and Interfaces

### 1. Header and Documentation

The header section will include:
- File title and description
- Version information (4.0)
- Last updated date (current date)
- Summary of included migrations (001-063)
- Brief overview of major features

### 2. Drop Existing Objects

This section will include statements to drop all existing objects in the correct order to avoid dependency issues:
- Drop views and materialized views first
- Drop tables with foreign key dependencies in reverse order
- Drop functions and procedures
- Drop custom types and enums

### 3. Extensions

This section will create any required PostgreSQL extensions:
- pg_trgm for text search optimization

### 4. Enums and Custom Types

This section will create all enum types used in the database:
- user_role
- truck_status
- driver_status
- location_type
- assignment_status
- trip_status (including the recently added 'exception_triggered' status)
- approval_status
- scan_type
- shift_type
- shift_status

### 5. Core Tables

This section will create the fundamental tables that other tables depend on:
- users
- dump_trucks
- drivers
- locations

Each table creation will include:
- All columns with their data types and constraints
- Primary keys and unique constraints
- Check constraints
- Default values
- Comments on tables and columns

### 6. Operational Tables

This section will create the tables related to daily operations:
- assignments (including dynamic assignment adaptation fields)
- trip_logs (including multi-location workflow fields)
- approvals (including dynamic assignment adaptation fields)
- scan_logs

### 7. Shift Management Tables

This section will create the tables related to the shift management system:
- driver_shifts
- shift_handovers

### 8. System Monitoring Tables

This section will create the tables related to system health monitoring:
- system_logs
- system_tasks
- system_health_logs

### 9. Indexes

This section will create all indexes for performance optimization:
- Standard indexes for frequently queried columns
- Composite indexes for common query patterns
- GIN indexes for JSONB columns
- Partial indexes with WHERE clauses for specific query patterns

### 10. Functions and Procedures

This section will create all functions and procedures:
- update_updated_at_column
- calculate_trip_durations
- update_assignment_on_trip_complete
- create_deviation_assignment
- get_exception_analytics
- get_advanced_exception_analytics
- refresh_trip_performance_summary
- get_database_performance_metrics
- evaluate_shift_status (with proper signature handling)
- schedule_auto_activation
- update_all_shift_statuses
- get_current_driver_for_truck
- create_shift_assignment

### 11. Triggers

This section will create all triggers:
- update_*_updated_at triggers for all tables
- trigger_calculate_trip_durations
- trigger_update_assignment_on_trip_complete

### 12. Views and Materialized Views

This section will create all views and materialized views:
- v_realtime_dashboard
- v_active_exceptions
- v_trip_performance
- v_trip_summary
- v_active_assignments
- mv_trip_performance_summary
- v_workflow_analytics
- v_dynamic_assignment_analytics

### 13. Initial Data Seeding

This section will include minimal data seeding for system initialization:
- Initial admin user
- System task record
- System health log record

### 14. Migration Tracking

This section will update the migration tracking table to indicate that all migrations up to 063 have been applied.

## Data Models

### Core Data Models

#### Users Table
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role user_role NOT NULL DEFAULT 'operator',
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    last_login TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### Dump Trucks Table
```sql
CREATE TABLE dump_trucks (
    id SERIAL PRIMARY KEY,
    truck_number VARCHAR(20) UNIQUE NOT NULL,
    license_plate VARCHAR(20) UNIQUE NOT NULL,
    make VARCHAR(50),
    model VARCHAR(50),
    year INTEGER,
    capacity_tons DECIMAL(5,2),
    qr_code_data JSONB NOT NULL,
    status truck_status NOT NULL DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### Drivers Table
```sql
CREATE TABLE drivers (
    id SERIAL PRIMARY KEY,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    license_number VARCHAR(30) UNIQUE NOT NULL,
    license_expiry DATE NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    hire_date DATE NOT NULL,
    status driver_status NOT NULL DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### Locations Table
```sql
CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    location_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    type location_type NOT NULL,
    address TEXT,
    coordinates VARCHAR(50),
    qr_code_data JSONB NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### Operational Data Models

#### Assignments Table (with Dynamic Assignment Adaptation)
```sql
CREATE TABLE assignments (
    id SERIAL PRIMARY KEY,
    assignment_code VARCHAR(50) UNIQUE,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER NOT NULL REFERENCES drivers(id) ON DELETE CASCADE,
    loading_location_id INTEGER NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
    unloading_location_id INTEGER NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
    status assignment_status NOT NULL DEFAULT 'assigned',
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    assigned_date DATE,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    expected_loads_per_day INTEGER DEFAULT 1,
    
    -- Dynamic Assignment Adaptation Fields
    is_adaptive BOOLEAN DEFAULT false,
    adaptation_strategy VARCHAR(50),
    adaptation_confidence VARCHAR(20),
    adaptation_metadata JSONB,
    
    -- Shift Management Integration
    shift_id INTEGER REFERENCES driver_shifts(id),
    is_shift_assignment BOOLEAN DEFAULT false,
    shift_handover_id INTEGER REFERENCES shift_handovers(id),
    
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### Trip Logs Table (with Multi-Location Workflow)
```sql
CREATE TABLE trip_logs (
    id SERIAL PRIMARY KEY,
    assignment_id INTEGER NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
    trip_number INTEGER NOT NULL,
    status trip_status NOT NULL DEFAULT 'assigned',
    
    -- Trip timestamps
    loading_start_time TIMESTAMP,
    loading_end_time TIMESTAMP,
    unloading_start_time TIMESTAMP,
    unloading_end_time TIMESTAMP,
    trip_completed_time TIMESTAMP,
    
    -- Actual locations
    actual_loading_location_id INTEGER REFERENCES locations(id),
    actual_unloading_location_id INTEGER REFERENCES locations(id),
    
    -- Exception handling
    is_exception BOOLEAN NOT NULL DEFAULT false,
    exception_reason TEXT,
    exception_approved_by INTEGER REFERENCES users(id),
    exception_approved_at TIMESTAMP,
    
    -- Performance metrics
    total_duration_minutes INTEGER,
    loading_duration_minutes INTEGER,
    travel_duration_minutes INTEGER,
    unloading_duration_minutes INTEGER,
    
    -- Multi-Location Workflow Fields
    location_sequence JSONB,
    is_extended_trip BOOLEAN DEFAULT FALSE,
    workflow_type VARCHAR(50) DEFAULT 'standard',
    baseline_trip_id INTEGER REFERENCES trip_logs(id) ON DELETE SET NULL,
    cycle_number INTEGER DEFAULT 1,
    
    notes JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(assignment_id, trip_number),
    CONSTRAINT chk_trip_timing_sequence CHECK (
        (loading_start_time IS NULL OR loading_end_time IS NULL OR loading_end_time >= loading_start_time) AND
        (loading_end_time IS NULL OR unloading_start_time IS NULL OR unloading_start_time >= loading_end_time) AND
        (unloading_start_time IS NULL OR unloading_end_time IS NULL OR unloading_end_time >= unloading_start_time) AND
        (unloading_end_time IS NULL OR trip_completed_time IS NULL OR trip_completed_time >= unloading_end_time)
    ),
    CONSTRAINT chk_duration_non_negative CHECK (
        (total_duration_minutes IS NULL OR total_duration_minutes >= 0) AND
        (loading_duration_minutes IS NULL OR loading_duration_minutes >= 0) AND
        (travel_duration_minutes IS NULL OR travel_duration_minutes >= 0) AND
        (unloading_duration_minutes IS NULL OR unloading_duration_minutes >= 0)
    ),
    CONSTRAINT chk_workflow_type CHECK (workflow_type IN ('standard', 'extended', 'cycle', 'dynamic')),
    CONSTRAINT chk_cycle_number CHECK (cycle_number >= 1)
);
```

### Shift Management Data Models

#### Driver Shifts Table
```sql
CREATE TABLE driver_shifts (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER NOT NULL REFERENCES drivers(id) ON DELETE CASCADE,
    shift_type shift_type NOT NULL DEFAULT 'day',
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status shift_status NOT NULL DEFAULT 'scheduled',
    
    -- Handover tracking
    previous_shift_id INTEGER REFERENCES driver_shifts(id),
    handover_notes TEXT,
    handover_completed_at TIMESTAMP,
    
    -- Assignment integration
    assignment_id INTEGER REFERENCES assignments(id),
    auto_created BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(truck_id, shift_date, start_time)
);
```

### System Monitoring Data Models

#### System Logs Table
```sql
CREATE TABLE system_logs (
    id SERIAL PRIMARY KEY,
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    user_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### System Tasks Table
```sql
CREATE TABLE system_tasks (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER,
    auto_executable BOOLEAN DEFAULT false,
    metadata JSONB,
    created_by INTEGER REFERENCES users(id)
);
```

## Error Handling

The initialization file will include robust error handling to ensure that the database is created correctly:

1. **Idempotent Operations**: All CREATE statements will use IF NOT EXISTS clauses where applicable to avoid errors if objects already exist.

2. **Dependency Management**: Objects will be created in the correct order to avoid dependency issues.

3. **Transaction Management**: The entire initialization script will be wrapped in a transaction to ensure atomicity.

4. **Constraint Validation**: Appropriate constraints will be added to ensure data integrity.

5. **Function Signature Conflicts**: The script will handle function signature conflicts by dropping all versions of a function before creating the correct version, as done in migration 062.

## Testing Strategy

The testing strategy for the updated initialization file will include:

1. **Clean Installation Test**: Execute the init.sql file on a clean database and verify that all objects are created without errors.

2. **Schema Comparison Test**: Compare the schema created by init.sql with a schema created by running all migrations sequentially to ensure they are identical.

3. **Functional Testing**: Perform basic application operations against a database initialized with init.sql to verify that the schema supports all required functionality.

4. **Migration Tracking Test**: Verify that the migration tracking table indicates that all migrations up to 063 have been applied.

5. **Performance Testing**: Verify that the indexes and optimizations are correctly applied by running performance tests on the initialized database.

## Implementation Approach

The implementation of the updated initialization file will follow these steps:

1. **Extract Current Schema**: Generate a complete schema dump from a database that has all migrations applied.

2. **Analyze Migrations**: Review all migration files (001-063) to understand the changes and ensure they are correctly incorporated.

3. **Organize Schema**: Restructure the schema dump into logical sections according to the file structure outlined above.

4. **Add Documentation**: Add comprehensive comments and documentation to explain the purpose and relationships of database objects.

5. **Optimize Script**: Review and optimize the script for clarity, performance, and maintainability.

6. **Test**: Thoroughly test the script to ensure it creates a fully functional database schema.

7. **Finalize**: Update the version information and last updated date in the header.

## Considerations and Trade-offs

### Considerations

1. **Readability vs. Compactness**: The script will prioritize readability and maintainability over compactness, using clear section headers and comments.

2. **Performance vs. Flexibility**: The script will include all performance optimizations from migrations, but will also maintain flexibility for future changes.

3. **Documentation Level**: The script will include comprehensive documentation to explain complex business logic and relationships.

4. **Error Handling**: The script will include robust error handling to ensure successful execution.

### Trade-offs

1. **Script Size**: The consolidated script will be larger than individual migrations, but will provide a complete reference for the schema.

2. **Execution Time**: The script may take longer to execute than individual migrations, but will only need to be run once for new deployments.

3. **Maintenance**: The script will require updates when new migrations are added, but will simplify the setup process for new environments.

4. **Complexity**: The script will be more complex than individual migrations, but will provide a more comprehensive view of the schema.