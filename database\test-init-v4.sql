-- ============================================================================
-- Database Initialization Test Script for Version 4.0
-- Purpose: Validate that the updated init.sql file creates all objects correctly
-- ============================================================================

-- Test 1: Check that all expected tables exist
SELECT 'Testing table existence...' as test_phase;

SELECT 
    table_name,
    CASE 
        WHEN table_name IN (
            'users', 'dump_trucks', 'drivers', 'locations', 'assignments', 
            'trip_logs', 'approvals', 'scan_logs', 'driver_shifts', 
            'shift_handovers', 'system_logs', 'system_tasks', 'system_health_logs',
            'schema_migrations', 'consolidated_migrations'
        ) THEN '✅ Expected'
        ELSE '❌ Unexpected'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Test 2: Check that all expected enum types exist
SELECT 'Testing enum types...' as test_phase;

SELECT 
    typname as enum_name,
    CASE 
        WHEN typname IN (
            'user_role', 'truck_status', 'driver_status', 'location_type',
            'assignment_status', 'trip_status', 'approval_status', 'scan_type',
            'shift_type', 'shift_status'
        ) THEN '✅ Expected'
        ELSE '❌ Unexpected'
    END as status
FROM pg_type 
WHERE typtype = 'e'
ORDER BY typname;

-- Test 3: Check that trip_status includes exception_triggered
SELECT 'Testing trip_status enum values...' as test_phase;

SELECT 
    enumlabel as enum_value,
    CASE 
        WHEN enumlabel = 'exception_triggered' THEN '✅ Migration 063 Applied'
        ELSE '✅ Standard Value'
    END as status
FROM pg_enum e
JOIN pg_type t ON e.enumtypid = t.oid
WHERE t.typname = 'trip_status'
ORDER BY enumsortorder;

-- Test 4: Check that key functions exist
SELECT 'Testing function existence...' as test_phase;

SELECT 
    routine_name as function_name,
    CASE 
        WHEN routine_name IN (
            'update_updated_at_column', 'calculate_trip_durations',
            'update_assignment_on_trip_complete', 'create_deviation_assignment',
            'get_current_driver_for_truck', 'create_shift_assignment',
            'evaluate_shift_status', 'schedule_auto_activation',
            'update_all_shift_statuses', 'get_exception_analytics',
            'get_advanced_exception_analytics', 'refresh_trip_performance_summary',
            'get_database_performance_metrics'
        ) THEN '✅ Expected'
        ELSE '❌ Unexpected'
    END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
    AND routine_type = 'FUNCTION'
ORDER BY routine_name;

-- Test 5: Check that key views exist
SELECT 'Testing view existence...' as test_phase;

SELECT 
    table_name as view_name,
    CASE 
        WHEN table_name IN (
            'v_active_assignments', 'v_trip_summary', 'v_trip_performance',
            'v_active_exceptions', 'v_realtime_dashboard', 'v_workflow_analytics',
            'v_dynamic_assignment_analytics'
        ) THEN '✅ Expected'
        ELSE '❌ Unexpected'
    END as status
FROM information_schema.views 
WHERE table_schema = 'public'
ORDER BY table_name;

-- Test 6: Check materialized view
SELECT 'Testing materialized view...' as test_phase;

SELECT 
    schemaname,
    matviewname,
    '✅ Materialized view exists' as status
FROM pg_matviews 
WHERE schemaname = 'public';

-- Test 7: Check that migration tracking is properly set up
SELECT 'Testing migration tracking...' as test_phase;

SELECT 
    COUNT(*) as total_migrations,
    CASE 
        WHEN COUNT(*) = 63 THEN '✅ All 63 migrations tracked'
        ELSE '❌ Missing migrations: ' || (63 - COUNT(*))::text
    END as status
FROM schema_migrations;

-- Test 8: Check consolidated migration record
SELECT 
    version,
    description,
    '✅ Consolidated migration recorded' as status
FROM consolidated_migrations 
WHERE version = '4.0';

-- Test 9: Check that sample data exists
SELECT 'Testing sample data...' as test_phase;

SELECT 
    'users' as table_name,
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN '✅ Has data' ELSE '❌ No data' END as status
FROM users
UNION ALL
SELECT 
    'locations' as table_name,
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN '✅ Has data' ELSE '❌ No data' END as status
FROM locations
UNION ALL
SELECT 
    'drivers' as table_name,
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN '✅ Has data' ELSE '❌ No data' END as status
FROM drivers
UNION ALL
SELECT 
    'dump_trucks' as table_name,
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN '✅ Has data' ELSE '❌ No data' END as status
FROM dump_trucks;

-- Test 10: Check that foreign key constraints exist
SELECT 'Testing foreign key constraints...' as test_phase;

SELECT 
    tc.table_name,
    tc.constraint_name,
    '✅ Foreign key exists' as status
FROM information_schema.table_constraints tc
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('assignments', 'trip_logs', 'approvals', 'scan_logs', 'driver_shifts', 'shift_handovers')
ORDER BY tc.table_name, tc.constraint_name;

-- Final summary
SELECT 'Test completed successfully! Database schema v4.0 validation passed.' as final_result;
