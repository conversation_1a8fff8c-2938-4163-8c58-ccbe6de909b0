-- ============================================================================
-- CONSOLIDATE EXISTING DATABASE
-- This script handles databases that already have all historical migrations applied
-- ============================================================================

-- First, let's check the current state
SELECT 'Current migration state:' as info;
SELECT COUNT(*) as total_migrations FROM migrations;

-- Check if we have the consolidation marker
SELECT 'Consolidation status:' as info;
SELECT 
    CASE WHEN EXISTS (SELECT 1 FROM migrations WHERE filename = '001_consolidated_schema.sql')
    THEN 'Already consolidated'
    ELSE 'Not consolidated yet'
    END as status;

-- Since you have all 64 historical migrations, we'll mark them as consolidated
-- This tells the system that your database is equivalent to the consolidated schema

INSERT INTO migrations (filename, executed_at)
VALUES ('001_consolidated_schema.sql', CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;

-- Add to migration log
INSERT INTO migration_log (migration_name, description)
VALUES (
    'Schema Consolidation v1.0',
    'Existing database with 64 historical migrations marked as consolidated'
);

-- Verify the consolidation
SELECT 'Consolidation completed:' as info;
SELECT 
    COUNT(*) as total_migrations,
    MAX(executed_at) as last_migration_date
FROM migrations;

SELECT 'Database is now consolidated and ready for future migrations starting from 065.' as result;