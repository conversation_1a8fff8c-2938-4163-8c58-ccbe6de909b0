const ValidationError = require('../utils/ValidationError');

describe('ValidationError', () => {
  describe('constructor', () => {
    it('should create a basic ValidationError', () => {
      const error = new ValidationError('Test error');
      
      expect(error.message).toBe('Test error');
      expect(error.name).toBe('ValidationError');
      expect(error.isValidationError).toBe(true);
      expect(error.details).toEqual({});
    });

    it('should create ValidationError with details', () => {
      const details = { field: 'test', code: 'TEST_ERROR' };
      const error = new ValidationError('Test error', details);
      
      expect(error.details).toEqual(details);
    });

    it('should throw TypeError for invalid message', () => {
      expect(() => new ValidationError('')).toThrow(TypeError);
      expect(() => new ValidationError(null)).toThrow(TypeError);
      expect(() => new ValidationError(123)).toThrow(TypeError);
    });

    it('should throw TypeError for invalid details', () => {
      expect(() => new ValidationError('test', 'invalid')).toThrow(TypeError);
      expect(() => new ValidationError('test', 123)).toThrow(TypeError);
    });

    it('should accept null details', () => {
      const error = new ValidationError('test', null);
      expect(error.details).toEqual({});
    });
  });

  describe('static factory methods', () => {
    describe('forField', () => {
      it('should create field validation error', () => {
        const error = ValidationError.forField('truck_number', 'is required', null);
        
        expect(error.message).toBe('truck_number: is required');
        expect(error.details.field).toBe('truck_number');
        expect(error.details.code).toBe('INVALID_TRUCK_NUMBER');
        expect(error.details.context.truck_number).toBeNull();
      });

      it('should include suggestion when provided', () => {
        const error = ValidationError.forField('truck_number', 'is invalid', 'T', 'Use format T123');
        
        expect(error.details.suggestion).toBe('Use format T123');
      });
    });

    describe('forWorkflow', () => {
      it('should create workflow validation error', () => {
        const error = ValidationError.forWorkflow('loading_start', 'complete trip', 'unloading_end');
        
        expect(error.message).toContain('Cannot complete trip from loading_start');
        expect(error.details.code).toBe('WORKFLOW_VIOLATION');
        expect(error.details.context.current_state).toBe('loading_start');
        expect(error.details.context.attempted_action).toBe('complete trip');
        expect(error.details.context.expected_state).toBe('unloading_end');
      });
    });

    describe('forBusinessRule', () => {
      it('should create business rule validation error', () => {
        const context = { location_type: 'loading', operation: 'unloading' };
        const error = ValidationError.forBusinessRule('Location type mismatch', context);
        
        expect(error.message).toBe('Business rule violation: Location type mismatch');
        expect(error.details.code).toBe('BUSINESS_RULE_VIOLATION');
        expect(error.details.context).toEqual(context);
      });
    });
  });

  describe('utility methods', () => {
    describe('toJSON', () => {
      it('should return structured JSON representation', () => {
        const error = new ValidationError('Test error', { field: 'test' });
        const json = error.toJSON();
        
        expect(json.error).toBe('ValidationError');
        expect(json.message).toBe('Test error');
        expect(json.details.field).toBe('test');
        expect(json.timestamp).toBeDefined();
        expect(new Date(json.timestamp)).toBeInstanceOf(Date);
      });
    });

    describe('isValidationError', () => {
      it('should identify ValidationError instances', () => {
        const validationError = new ValidationError('test');
        const regularError = new Error('test');
        const mockValidationError = { isValidationError: true };
        
        expect(ValidationError.isValidationError(validationError)).toBe(true);
        expect(ValidationError.isValidationError(regularError)).toBe(false);
        expect(ValidationError.isValidationError(mockValidationError)).toBe(true);
        expect(ValidationError.isValidationError(null)).toBe(false);
      });
    });
  });

  describe('inheritance', () => {
    it('should be instance of Error', () => {
      const error = new ValidationError('test');
      
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(ValidationError);
    });

    it('should have proper stack trace', () => {
      const error = new ValidationError('test');
      
      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('ValidationError: test');
    });
  });
});