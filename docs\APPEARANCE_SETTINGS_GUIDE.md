# Appearance Settings Guide

## Overview

The Hauling QR Trip Management System includes comprehensive appearance customization options that allow organizations to brand and customize the visual appearance of their application. This feature is accessible through the Settings panel and provides real-time preview capabilities.

## Features

### 🖼️ Logo Customization

#### Supported Logo Options
- **Custom Logo URL**: Enter any publicly accessible image URL
- **Logo Dimensions**: Adjustable width and height (20-200px range)
- **Alt Text**: Customizable accessibility text for screen readers
- **Live Preview**: Real-time preview of logo changes before applying

#### Logo Requirements
- **Supported Formats**: PNG, JPEG, GIF, WebP, SVG
- **Recommended Size**: 40x40px to 120x120px for optimal display
- **Accessibility**: Always provide meaningful alt text
- **Performance**: Use optimized images for faster loading

#### Logo Implementation
The logo system is integrated into the application sidebar with the following features:

**Dynamic Logo Display**:
- Custom logos are displayed when a valid URL is provided
- Automatic fallback to default truck emoji (🚛) when no logo is set
- Error handling with graceful fallback if logo fails to load

**CSS Integration**:
The logo system uses CSS custom properties for dynamic sizing and font styling:
```css
/* Logo Properties */
--logo-width: '40px'
--logo-height: '40px'

/* Typography Properties Applied to Logo Text */
.font-custom-header {
  font-family: var(--font-header-family);
  font-size: var(--font-header-size);
  font-weight: var(--font-header-weight);
}

.font-custom-footer {
  font-family: var(--font-footer-family);
  font-size: var(--font-footer-size);
  font-weight: var(--font-footer-weight);
}
```

**Sidebar Integration**:
- Logo appears in desktop sidebar header with full customization support
- Mobile sidebar currently uses default truck emoji (🚛) - custom logo support planned
- Responsive design maintains logo visibility across all screen sizes
- Logo dimensions are applied via inline styles for precise control
- Alt text is properly set for accessibility compliance
- Error handling gracefully falls back to default icon if custom logo fails to load

### 🔤 Typography Settings

#### Font Family Options
The system includes 8 professional font families:

1. **Inter** (Default) - Modern, highly legible sans-serif
2. **Arial** - Classic, widely supported sans-serif
3. **Helvetica** - Clean, professional sans-serif
4. **Georgia** - Elegant, readable serif font
5. **Times New Roman** - Traditional serif font
6. **Courier New** - Monospace font for technical content
7. **Verdana** - Web-optimized sans-serif
8. **Trebuchet MS** - Friendly, rounded sans-serif

#### Font Size Ranges
- **Headers**: 18px - 32px (default: 24px)
- **Content**: 12px - 22px (default: 16px)
- **Footer**: 10px - 18px (default: 14px)

#### Font Weight Options
- **Light (300)** - Subtle, minimal emphasis
- **Normal (400)** - Standard text weight
- **Medium (500)** - Slightly emphasized
- **Semi Bold (600)** - Strong emphasis (default for headers)
- **Bold (700)** - Maximum emphasis

#### Typography Implementation
Font settings are applied using CSS custom properties:
```css
/* Header Typography */
--font-header-family: 'Inter, system-ui, sans-serif'
--font-header-size: '24px'
--font-header-weight: '600'

/* Content Typography */
--font-content-family: 'Inter, system-ui, sans-serif'
--font-content-size: '16px'
--font-content-weight: '400'

/* Footer Typography */
--font-footer-family: 'Inter, system-ui, sans-serif'
--font-footer-size: '14px'
--font-footer-weight: '400'
```

## Usage Guide

### Accessing Appearance Settings

1. **Navigate to Settings**
   - Click on the main menu
   - Select "⚙️ Settings"

2. **Open Appearance Settings**
   - Click on "🎨 Appearance Settings" card
   - The settings panel will open with all customization options

### Customizing Your Logo

1. **Enter Logo URL**
   - Paste your logo's public URL in the "Logo Image URL" field
   - The preview will update automatically

2. **Adjust Dimensions**
   - Use the width and height controls to resize your logo
   - Maintain aspect ratio for best results

3. **Set Alt Text**
   - Provide descriptive alt text for accessibility
   - Default: "Hauling QR System"

4. **Preview Changes**
   - Enable "Preview Mode" to see changes applied temporarily
   - Toggle off to revert to saved settings

### Customizing Typography

1. **Select Font Families**
   - Choose different fonts for headers, content, and footer
   - Each section can have independent font settings

2. **Adjust Font Sizes**
   - Use dropdown menus to select appropriate sizes
   - Consider readability on mobile devices

3. **Set Font Weights**
   - Choose weights that provide good hierarchy
   - Headers typically use Semi Bold (600)
   - Content uses Normal (400)

### Preview and Save

1. **Preview Mode**
   - Toggle "👁️ Preview On" to see changes in real-time
   - Changes are applied temporarily using CSS custom properties
   - Navigate through the app to see how changes look

2. **Save Settings**
   - Click "Save Settings" to make changes permanent
   - Settings are stored in localStorage
   - Applied automatically on future visits

3. **Reset to Defaults**
   - Click "Reset to Defaults" to restore original settings
   - Clears all customizations and localStorage data

## Technical Implementation

### CSS Custom Properties

The appearance system leverages CSS custom properties (CSS variables) for dynamic theming:

```css
:root {
  /* Logo Properties */
  --logo-width: 40px;
  --logo-height: 40px;
  
  /* Typography Properties */
  --font-header-family: 'Inter, system-ui, sans-serif';
  --font-header-size: 24px;
  --font-header-weight: 600;
  --font-content-family: 'Inter, system-ui, sans-serif';
  --font-content-size: 16px;
  --font-content-weight: 400;
  --font-footer-family: 'Inter, system-ui, sans-serif';
  --font-footer-size: 14px;
  --font-footer-weight: 400;
}
```

### Data Persistence

Settings are stored in the browser's localStorage:

```javascript
// Storage key
'hauling_appearance_settings'

// Data structure
{
  logo: {
    src: 'https://example.com/logo.png',
    alt: 'Company Logo',
    width: 60,
    height: 60
  },
  fonts: {
    header: {
      family: 'Georgia, serif',
      size: '28px',
      weight: '700'
    },
    content: {
      family: 'Georgia, serif',
      size: '18px',
      weight: '400'
    },
    footer: {
      family: 'Arial, sans-serif',
      size: '14px',
      weight: '400'
    }
  }
}
```

### Component Architecture

```
AppearanceSettings/
├── State Management (useState)
├── Settings Persistence (useEffect + localStorage)
├── CSS Property Application (useEffect + document.documentElement)
├── Logo Configuration Section
├── Typography Configuration Section
├── Preview Mode Toggle
└── Save/Reset Actions
```

## Best Practices

### Logo Guidelines

1. **Image Quality**
   - Use high-resolution images (2x for retina displays)
   - Optimize file size for web performance
   - Test on different backgrounds

2. **Accessibility**
   - Always provide meaningful alt text
   - Ensure sufficient contrast with background
   - Test with screen readers

3. **Responsive Design**
   - Test logo at different sizes
   - Consider mobile display constraints
   - Maintain readability at small sizes

### Typography Guidelines

1. **Hierarchy**
   - Use consistent font weights for hierarchy
   - Headers should be larger and bolder than content
   - Footer text can be smaller and lighter

2. **Readability**
   - Ensure sufficient contrast ratios
   - Test on mobile devices
   - Consider users with visual impairments

3. **Performance**
   - Limit the number of font families
   - Use system fonts when possible
   - Consider font loading performance

### Testing Recommendations

1. **Cross-Browser Testing**
   - Test in Chrome, Firefox, Safari, Edge
   - Verify font rendering consistency
   - Check CSS custom property support

2. **Mobile Testing**
   - Test on actual mobile devices
   - Verify touch targets remain accessible
   - Check text readability at small sizes

3. **Accessibility Testing**
   - Use screen readers to test alt text
   - Verify color contrast ratios
   - Test keyboard navigation

## Troubleshooting

### Common Issues

#### Logo Not Displaying
- **Check URL**: Ensure the image URL is publicly accessible
- **CORS Issues**: Some image hosts block cross-origin requests
- **File Format**: Verify the image format is supported
- **Network**: Check internet connection and firewall settings

#### Font Not Loading
- **Browser Support**: Verify the font is supported in the target browser
- **Fallback Fonts**: System fonts should always work as fallbacks
- **CSS Application**: Check if custom properties are being applied

#### Settings Not Persisting
- **localStorage**: Ensure localStorage is enabled in the browser
- **Private Browsing**: Settings won't persist in incognito/private mode
- **Storage Quota**: Check if localStorage quota is exceeded

#### Preview Mode Issues
- **CSS Properties**: Verify CSS custom properties are supported
- **Property Names**: Check for typos in CSS property names
- **Specificity**: Ensure custom properties aren't being overridden

### Debug Steps

1. **Check Browser Console**
   - Look for JavaScript errors
   - Verify API calls are successful
   - Check for CSS warnings

2. **Inspect CSS Properties**
   - Use browser dev tools to inspect `:root` element
   - Verify custom properties are applied
   - Check computed styles

3. **Test localStorage**
   - Open browser dev tools → Application → Local Storage
   - Verify settings are being saved
   - Check data format and structure

## Future Enhancements

### Planned Features
- **Mobile Sidebar Logo Support**: Complete logo customization integration for mobile sidebar
- **Color Theme Customization**: Custom color palettes
- **Dark Mode Support**: Automatic dark/light theme switching
- **Advanced Typography**: Line height, letter spacing controls
- **Logo Upload**: Direct file upload instead of URL only
- **Theme Presets**: Pre-configured appearance themes
- **Export/Import**: Share appearance settings between instances

### Integration Opportunities
- **Brand Guidelines**: Integration with corporate brand standards
- **Multi-tenant Support**: Different appearances for different organizations
- **Admin Controls**: Centralized appearance management
- **API Integration**: Server-side appearance configuration

---

For technical support or questions about appearance customization, please contact the development team or refer to the main documentation.