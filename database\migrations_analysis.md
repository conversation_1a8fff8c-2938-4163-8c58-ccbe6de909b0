# Migration Consolidation Analysis

## Current State
- **Total migrations**: 61 files (001-060, with one duplicate 060)
- **Major issues identified**: Redundant fixes, multiple attempts at same problems, scattered related changes

## Consolidation Opportunities

### Group 1: Approvals Table Schema Fixes (002, 003, 006, 025)
**Files to consolidate:**
- 002_fix_approvals_table_schema.sql
- 003_fix_approvals_schema_corrected.sql  
- 006_fix_approvals_table_schema.sql
- 025_add_notes_column_to_approvals.sql

**Issues:** Multiple attempts to fix the same approvals table schema issues

### Group 2: Scan Logs Foreign Key Fixes (004, 005)
**Files to consolidate:**
- 004_scan_logs_location_on_delete_set_null.sql
- 005_ensure_scan_logs_location_on_delete_set_null.sql

**Issues:** Duplicate attempts to fix the same foreign key constraint

### Group 3: Assignment Table Enhancements (001, 009, 013, 014, 020)
**Files to consolidate:**
- 001_add_assignment_code_priority.sql
- 009_add_driver_rate_column.sql (later removed in 020)
- 013_make_assigned_date_optional.sql
- 014_add_missing_assignments_columns.sql
- 020_remove_rate_calculation_system.sql

**Issues:** Scattered assignment table modifications, including adding then removing driver_rate

### Group 4: Shift Management System (017, 018, 021, 022, 023, 024)
**Files to consolidate:**
- 017_multi_driver_shifts.sql
- 018_fix_assignment_database_issues.sql
- 021_create_enhanced_assignments_view.sql
- 022_fix_shift_date_filtering.sql
- 023_enhance_shift_date_ranges.sql
- 024_fix_shift_constraint.sql

**Issues:** Related shift management features spread across multiple migrations

### Group 5: Debug Function Fixes (058, 059, 060, 060_improved)
**Files to consolidate:**
- 058_fix_debug_shift_status_function.sql
- 059_cleanup_debug_function.sql
- 060_fix_debug_function_columns.sql
- 060_fix_debug_function_columns_improved.sql

**Issues:** Multiple attempts to fix the same debug function

### Group 6: System Health & Logging (049, 050, 051, 055)
**Files to consolidate:**
- 049_create_permanent_health_checks.sql
- 050_create_automated_fix_logs.sql
- 051_create_system_tasks_and_health_logs.sql
- 055_ensure_system_logs_table.sql

**Issues:** Related system monitoring features

## Consolidation Strategy

1. **Preserve migration history** - Keep original files as backup
2. **Create new consolidated migrations** with proper sequencing
3. **Test thoroughly** to ensure same final schema
4. **Update migration tracking** to handle the consolidation

## Benefits
- Reduce from 61 to approximately 35-40 migration files
- Eliminate redundant operations
- Improve migration performance
- Cleaner, more logical migration sequence
- Easier maintenance and understanding