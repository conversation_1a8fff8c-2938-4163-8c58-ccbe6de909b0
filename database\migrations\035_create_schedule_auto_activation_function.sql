-- Migration: Create schedule_auto_activation function
-- Purpose: Auto-activate shifts based on current date/time
-- Created: 2025-07-14

CREATE OR REPLACE FUNCTION schedule_auto_activation() RETURNS void AS $$
DECLARE
    activated_count INTEGER := 0;
    completed_count INTEGER := 0;
BEGIN
    -- Auto-activate scheduled shifts that should be starting now
    UPDATE driver_shifts
    SET status = 'active', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'scheduled'
        AND (
            (recurrence_pattern = 'single' AND shift_date = CURRENT_DATE)
            OR
            (recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN start_date AND end_date)
        )
        AND (
            -- For regular shifts (same day)
            (end_time >= start_time AND CURRENT_TIME >= start_time AND CURRENT_TIME < end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (end_time < start_time AND (CURRENT_TIME >= start_time OR CURRENT_TIME < end_time))
        );

    GET DIAGNOSTICS activated_count = ROW_COUNT;

    -- Auto-complete active shifts that should be ending now
    UPDATE driver_shifts
    SET status = 'completed', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active'
        AND (
            (recurrence_pattern = 'single' AND shift_date = CURRENT_DATE)
            OR
            (recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN start_date AND end_date)
        )
        AND (
            -- For regular shifts (same day)
            (end_time >= start_time AND CURRENT_TIME >= end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (end_time < start_time AND CURRENT_TIME >= end_time AND CURRENT_TIME < start_time)
        );

    GET DIAGNOSTICS completed_count = ROW_COUNT;

    -- Log the operation
    RAISE NOTICE 'Auto-activation completed: % activated, % completed', activated_count, completed_count;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION schedule_auto_activation() TO public;

-- Add comment
COMMENT ON FUNCTION schedule_auto_activation() IS 'Automatically activates scheduled shifts and completes active shifts based on current date/time';