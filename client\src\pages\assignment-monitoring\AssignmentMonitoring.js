import React, { useState, useEffect } from 'react';
import { assignmentsAPI, trucksAPI, analyticsAPI } from '../../services/api';
import toast from 'react-hot-toast';

const AssignmentMonitoring = () => {
  const [assignments, setAssignments] = useState([]);
  const [trucks, setTrucks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalAssignments: 0,
    activeAssignments: 0,
    autoCreatedToday: 0,
    completionRate: 0
  });

  useEffect(() => {
    fetchData();
    // Refresh data every 30 seconds
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Get comprehensive assignment data from analytics API for better data
      const [assignmentsResponse, trucksResponse, analyticsResponse] = await Promise.all([
        assignmentsAPI.getAll(),
        trucksAPI.getAll(),
        analyticsAPI.getAssignments({
          start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end_date: new Date().toISOString().split('T')[0]
        }).catch(err => {
          console.warn('Analytics API failed, continuing with assignments only:', err.message);
          return { data: null };
        })
      ]);

      // Process assignment data with enhanced error handling
      let assignmentsData = [];
      if (assignmentsResponse?.data?.data) {
        // Handle paginated response format
        assignmentsData = Array.isArray(assignmentsResponse.data.data) ? assignmentsResponse.data.data : [];
      } else if (assignmentsResponse?.data && Array.isArray(assignmentsResponse.data)) {
        // Handle direct array response format
        assignmentsData = assignmentsResponse.data;
      }

      console.log('Assignment Monitoring - Assignments loaded:', assignmentsData.length);

      // Process trucks data
      let trucksData = [];
      if (trucksResponse?.data?.data) {
        // Handle paginated response format
        trucksData = Array.isArray(trucksResponse.data.data) ? trucksResponse.data.data : [];
      } else if (trucksResponse?.data && Array.isArray(trucksResponse.data)) {
        // Handle direct array response format
        trucksData = trucksResponse.data;
      }

      console.log('Assignment Monitoring - Trucks loaded:', trucksData.length);

      // Get analytics data for better stats
      const analyticsData = analyticsResponse?.data?.data;
      
      // If we have analytics data, use it for more accurate stats
      if (analyticsData && analyticsData.recent) {
        // Merge analytics recent assignments with regular assignments
        const enhancedAssignments = analyticsData.recent.map(analyticsAssignment => {
          const existingAssignment = assignmentsData.find(a => a.id === analyticsAssignment.id);
          return {
            ...existingAssignment,
            ...analyticsAssignment,
            // Ensure we have location names from analytics
            loading_location_name: analyticsAssignment.loading_location || existingAssignment?.loading_location_name,
            unloading_location_name: analyticsAssignment.unloading_location || existingAssignment?.unloading_location_name
          };
        });
        
        // Use enhanced assignments if we have them
        if (enhancedAssignments.length > 0) {
          assignmentsData = enhancedAssignments;
        }
      }

      setAssignments(assignmentsData);
      setTrucks(trucksData);

      // Calculate stats using both regular assignments and analytics data
      const today = new Date().toISOString().split('T')[0];
      
      // Use analytics summary if available, otherwise calculate from assignments
      if (analyticsData && analyticsData.summary) {
        setStats({
          totalAssignments: analyticsData.summary.total || assignmentsData.length,
          activeAssignments: analyticsData.summary.active || assignmentsData.filter(a => a.status === 'assigned' || a.status === 'in_progress').length,
          autoCreatedToday: analyticsData.summary.autoCreated || 0,
          completionRate: parseFloat(analyticsData.summary.completionRate) || 0
        });
      } else {
        // Fallback to manual calculation
        const activeAssignments = assignmentsData.filter(a => a.status === 'assigned' || a.status === 'in_progress');
        const autoCreatedToday = assignmentsData.filter(a => {
          const isToday = a.assigned_date === today || a.created_at?.includes(today);
          const isAutoCreated = (a.notes &&
            ((typeof a.notes === 'string' && a.notes.includes('auto_assignment')) ||
             (typeof a.notes === 'object' && (a.notes.type === 'auto_assignment' || a.notes.auto_created)))) ||
            a.assignment_code?.startsWith('ASG-') ||
            a.assignment_code?.startsWith('DYN-');
          return isToday && isAutoCreated;
        });

        setStats({
          totalAssignments: assignmentsData.length,
          activeAssignments: activeAssignments.length,
          autoCreatedToday: autoCreatedToday.length,
          completionRate: assignmentsData.length > 0 ?
            Math.round((assignmentsData.filter(a => a.status === 'completed').length / assignmentsData.length) * 100) : 0
        });
      }

    } catch (error) {
      console.error('Error fetching assignment data:', error);
      toast.error('Failed to load assignment monitoring data');
      // Ensure state is reset to empty arrays on error
      setAssignments([]);
      setTrucks([]);
      setStats({
        totalAssignments: 0,
        activeAssignments: 0,
        autoCreatedToday: 0,
        completionRate: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusColors = {
      'assigned': 'bg-blue-100 text-blue-800',
      'in_progress': 'bg-yellow-100 text-yellow-800',
      'completed': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Assignment Monitoring</h1>
            <p className="mt-1 text-sm text-gray-600">
              Monitor dynamic assignment creation and truck assignment status
            </p>
          </div>
          <button
            onClick={fetchData}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">📋</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Assignments</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.totalAssignments}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">✅</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Assignments</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.activeAssignments}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">🤖</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Auto-Created Today</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.autoCreatedToday}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">📊</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Completion Rate</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.completionRate}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Assignments Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Assignments</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Latest assignment activities and status updates
          </p>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assignment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Truck
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Route
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(Array.isArray(assignments) ? assignments : []).slice(0, 20).length > 0 ? (
                (Array.isArray(assignments) ? assignments : []).slice(0, 20).map((assignment) => {
                  const truck = trucks.find(t => t.id === assignment.truck_id);
                  
                  // Enhanced auto-creation detection
                  const isAutoCreated = assignment.is_auto_created ||
                    (assignment.notes &&
                      ((typeof assignment.notes === 'string' && assignment.notes.includes('auto_assignment')) ||
                       (typeof assignment.notes === 'object' && (assignment.notes.type === 'auto_assignment' ||
                                                                assignment.notes.auto_created ||
                                                                assignment.notes.creation_method === 'auto_assignment' ||
                                                                assignment.notes.creation_method === 'dynamic_assignment')))) ||
                    assignment.assignment_code?.startsWith('ASG-') ||
                    assignment.assignment_code?.startsWith('DYN-');

                  // Enhanced auto-creation type detection
                  let autoType = 'Manual';
                  let autoIcon = '👤';
                  let autoColor = 'bg-gray-100 text-gray-800';
                  
                  if (isAutoCreated) {
                    if (assignment.assignment_code?.startsWith('DYN-') ||
                        (assignment.notes && (assignment.notes.creation_method === 'dynamic_assignment' ||
                                              assignment.notes.route_discovery))) {
                      autoType = 'Dynamic Route';
                      autoIcon = '🔄';
                      autoColor = 'bg-blue-100 text-blue-800';
                    } else {
                      autoType = 'Auto-Created';
                      autoIcon = '🤖';
                      autoColor = 'bg-purple-100 text-purple-800';
                    }
                  }

                  return (
                    <tr key={assignment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {assignment.assignment_code || `ASG-${assignment.id}`}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {assignment.truck_number || truck?.truck_number || 'Unknown'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex flex-col">
                          <span className="font-medium">{assignment.loading_location_name || assignment.loading_location || 'Unknown'}</span>
                          <span className="text-gray-500">→ {assignment.unloading_location_name || assignment.unloading_location || 'Unknown'}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(assignment.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDateTime(assignment.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${autoColor}`}>
                          {autoIcon} {autoType}
                        </span>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                    No assignments found. Assignment data will appear here when available.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AssignmentMonitoring;
