# ADR 0007: Shift Cache Invalidation for Overnight Shift Display Bug

## Status
**Accepted** - Implemented and deployed

## Date
2025-07-14

## Context

### Problem Statement
The workforce management system was displaying "⚠️ No Active Shift" for <PERSON> (DR-002) on DT-100 despite having an active night shift (18:00-06:00). This occurred specifically during overnight hours (midnight-6AM).

### Root Cause Analysis
The issue was traced to **incorrect time range calculation** in the `ShiftDisplayHelper.js` utility:

1. **Flawed Logic**: Used `BETWEEN` operator with adjusted end time for overnight shifts
2. **Cache Invalidation**: Cache keys didn't include time-sensitive parameters
3. **Data Flow**: Shift status changes weren't triggering cache invalidation

### Technical Details
- **Location**: `server/utils/ShiftDisplayHelper.js` lines 63-73 and 174-184
- **Impact**: All overnight shifts (start_time > end_time) were incorrectly filtered
- **Cache TTL**: 30 seconds, but keys didn't include current time context

## Decision

### Chosen Solution
**Implement proper overnight shift time calculation with enhanced cache invalidation**

#### Changes Made:
1. **Fixed Time Logic**: Replaced `BETWEEN` with proper overnight shift detection
2. **Enhanced Cache Keys**: Added time-sensitive parameters to cache keys
3. **Cache Invalidation**: Added automatic cache clearing on shift status changes
4. **Monitoring**: Added "Apply Overnight Fix" button in Settings

### Implementation Details

#### 1. Time Calculation Fix
```sql
-- Before (broken)
CURRENT_TIME BETWEEN start_time AND 
    CASE 
        WHEN end_time < start_time 
        THEN end_time + interval '24 hours' 
        ELSE end_time 
    END

-- After (fixed)
(
    -- Regular shifts (same day)
    (end_time >= start_time AND CURRENT_TIME BETWEEN start_time AND end_time)
    OR
    -- Overnight shifts (crosses midnight)
    (end_time < start_time AND (CURRENT_TIME >= start_time OR CURRENT_TIME <= end_time))
)
```

#### 2. Cache Enhancement
- **Cache Key**: Now includes `truck_id + current_date + current_hour`
- **Invalidation**: Automatic on shift status changes
- **TTL**: Reduced to 15 seconds for more responsive updates

#### 3. Monitoring
- Added "Apply Overnight Fix" button in Shift Synchronization Monitor
- Enhanced logging for debugging overnight shift issues

## Consequences

### Positive
- ✅ Overnight shifts now display correctly
- ✅ Real-time updates when shift status changes
- ✅ Better debugging capabilities
- ✅ Comprehensive test coverage

### Negative
- ⚠️ Slightly increased database queries (15s vs 30s cache TTL)
- ⚠️ Additional complexity in time calculation logic

### Neutral
- No breaking changes to existing API contracts
- Backward compatible with existing shift data

## Testing Strategy

### Unit Tests
- Test overnight shift time calculation
- Test cache invalidation on status changes
- Test edge cases (midnight boundaries)

### Integration Tests
- Test full data flow from roster to UI
- Test cache behavior under load
- Test concurrent shift updates

### Manual Testing
- Verified Maria Garcia (DR-002) displays correctly on DT-100
- Tested across different time zones
- Verified cache invalidation works

## Deployment Notes

### Rollback Plan
1. Revert `ShiftDisplayHelper.js` changes
2. Clear all cache keys
3. Restart application

### Monitoring
- Monitor `/api/shifts/current` response times
- Track cache hit/miss ratios
- Monitor overnight shift display accuracy

## References
- [Bug Report #1234](https://github.com/hauling-qr-system/issues/1234)
- [Test Results](test/shift-display.test.js)
- [Fix Script](scripts/fix-shift-cache.js)