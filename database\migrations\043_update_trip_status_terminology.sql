-- ============================================================================
-- Migration 037: Update Trip Status Terminology (breakdown → stopped)
-- Purpose: Replace 'breakdown' with 'stopped' in trip_status enum for more general terminology
-- Date: 2025-01-09
-- ============================================================================

-- Log the migration start
DO $$
BEGIN
    RAISE NOTICE 'Starting trip status terminology update: breakdown → stopped';
END $$;

-- ============================================================================
-- Step 1: Add 'stopped' to existing enum if it doesn't exist
-- ============================================================================

-- Add 'stopped' to the enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum e 
        JOIN pg_type t ON e.enumtypid = t.oid 
        WHERE t.typname = 'trip_status' AND e.enumlabel = 'stopped'
    ) THEN
        ALTER TYPE trip_status ADD VALUE 'stopped';
        RAISE NOTICE 'Added "stopped" to trip_status enum';
    END IF;
END $$;

-- ============================================================================
-- Step 2: Update existing breakdown trips to stopped status
-- ============================================================================

-- Note: 'breakdown' was never a valid trip_status enum value
-- This step is skipped as no trips would have 'breakdown' status
-- The 'stopped' status is added for new functionality

-- Log the update (no-op)
DO $$
BEGIN
    RAISE NOTICE 'Skipped updating breakdown trips - breakdown was never a valid status';
END $$;

-- ============================================================================
-- Step 3: Create basic helper functions
-- ============================================================================

-- Function to check if a trip is in a terminal state
CREATE OR REPLACE FUNCTION is_trip_terminal(p_status trip_status)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN p_status IN ('trip_completed', 'stopped', 'cancelled');
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Migration Complete
-- ============================================================================

-- Final success message
DO $$
BEGIN
    RAISE NOTICE 'Migration 037 completed successfully: trip_status terminology updated';
    RAISE NOTICE '  - Added "stopped" status to trip_status enum';
    RAISE NOTICE '  - No breakdown trips to migrate (breakdown was never a valid status)';
END $$;
