/**
 * Unit tests for AutomatedFixService
 * 
 * Tests the automated fix functionality for system health issues
 * including shift, assignment, and trip fixes.
 */

const AutomatedFixService = require('../../services/AutomatedFixService');
const db = require('../../config/database');

// Mock dependencies
jest.mock('../../config/database', () => ({
  query: jest.fn(),
  pool: {
    query: jest.fn()
  }
}));

describe('AutomatedFixService', () => {
  let fixService;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create instance of service
    fixService = new AutomatedFixService();
  });
  
  describe('fixShiftManagement', () => {
    it('should fix shift management issues', async () => {
      // Mock database responses
      db.pool.query
        // First query - get shift issues
        .mockResolvedValueOnce({
          rows: [
            { shift_id: 'shift_123', truck_id: 'DT-100', issue_type: 'status_mismatch' },
            { shift_id: 'shift_124', truck_id: 'DT-101', issue_type: 'activation_failure' }
          ]
        })
        // Second query - fix shifts
        .mockResolvedValueOnce({
          rows: [
            { shift_id: 'shift_123', truck_id: 'DT-100', status: 'active' },
            { shift_id: 'shift_124', truck_id: 'DT-101', status: 'active' }
          ]
        })
        // Third query - verify fixes
        .mockResolvedValueOnce({
          rows: []
        });
      
      // Call the method
      const result = await fixService.fixShiftManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.details.before.issueCount).toBe(2);
      expect(result.details.after.issueCount).toBe(0);
      expect(result.affectedRecords).toHaveLength(2);
      expect(result.affectedRecords).toContain('shift_123');
      expect(result.affectedRecords).toContain('shift_124');
      
      // Verify database calls
      expect(db.pool.query).toHaveBeenCalledTimes(3);
    });
    
    it('should handle case with no issues to fix', async () => {
      // Mock database response with no issues
      db.pool.query.mockResolvedValueOnce({
        rows: []
      });
      
      // Call the method
      const result = await fixService.fixShiftManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.details.before.issueCount).toBe(0);
      expect(result.details.after.issueCount).toBe(0);
      expect(result.affectedRecords).toHaveLength(0);
      
      // Verify database calls - should only make the initial check
      expect(db.pool.query).toHaveBeenCalledTimes(1);
    });
    
    it('should handle database errors', async () => {
      // Mock database error
      db.pool.query.mockRejectedValueOnce(new Error('Database connection failed'));
      
      // Call the method and expect it to throw
      await expect(fixService.fixShiftManagement()).rejects.toThrow('Database connection failed');
    });
  });
  
  describe('fixAssignmentManagement', () => {
    it('should fix assignment management issues', async () => {
      // Mock database responses
      db.pool.query
        // First query - get assignment issues
        .mockResolvedValueOnce({
          rows: [
            { truck_id: 'DT-100', driver_id: 'D-001', display_status: 'No Active Shift', shift_status: 'active' },
            { truck_id: 'DT-101', driver_id: 'D-002', display_status: 'No Active Shift', shift_status: 'active' }
          ]
        })
        // Second query - fix assignments
        .mockResolvedValueOnce({
          rows: [
            { truck_id: 'DT-100', driver_id: 'D-001', display_status: 'Active', shift_status: 'active' },
            { truck_id: 'DT-101', driver_id: 'D-002', display_status: 'Active', shift_status: 'active' }
          ]
        })
        // Third query - verify fixes
        .mockResolvedValueOnce({
          rows: []
        });
      
      // Call the method
      const result = await fixService.fixAssignmentManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.details.before.noActiveShiftCount).toBe(2);
      expect(result.details.after.noActiveShiftCount).toBe(0);
      expect(result.affectedRecords).toHaveLength(2);
      expect(result.affectedRecords).toContain('DT-100');
      expect(result.affectedRecords).toContain('DT-101');
      
      // Verify database calls
      expect(db.pool.query).toHaveBeenCalledTimes(3);
    });
    
    it('should handle case with no issues to fix', async () => {
      // Mock database response with no issues
      db.pool.query.mockResolvedValueOnce({
        rows: []
      });
      
      // Call the method
      const result = await fixService.fixAssignmentManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.details.before.noActiveShiftCount).toBe(0);
      expect(result.details.after.noActiveShiftCount).toBe(0);
      expect(result.affectedRecords).toHaveLength(0);
      
      // Verify database calls - should only make the initial check
      expect(db.pool.query).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('fixTripMonitoring', () => {
    it('should fix trip workflow issues', async () => {
      // Mock database responses
      db.pool.query
        // First query - get trip issues
        .mockResolvedValueOnce({
          rows: [
            { trip_id: 'T-001', truck_id: 'DT-100', status: 'VERIFIED', workflow_valid: false, issue: 'Missing COMPLETED state' },
            { trip_id: 'T-002', truck_id: 'DT-101', status: 'IN_PROGRESS', workflow_valid: false, issue: 'Stuck in progress' }
          ]
        })
        // Second query - fix trips
        .mockResolvedValueOnce({
          rows: [
            { trip_id: 'T-001', truck_id: 'DT-100', previous_status: 'VERIFIED', new_status: 'COMPLETED' },
            { trip_id: 'T-002', truck_id: 'DT-101', previous_status: 'IN_PROGRESS', new_status: 'COMPLETED' }
          ]
        })
        // Third query - verify fixes
        .mockResolvedValueOnce({
          rows: []
        });
      
      // Call the method
      const result = await fixService.fixTripMonitoring();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.details.fixedTrips).toHaveLength(2);
      expect(result.details.fixedTrips[0].trip_id).toBe('T-001');
      expect(result.details.fixedTrips[1].trip_id).toBe('T-002');
      expect(result.affectedRecords).toHaveLength(2);
      expect(result.affectedRecords).toContain('T-001');
      expect(result.affectedRecords).toContain('T-002');
      
      // Verify database calls
      expect(db.pool.query).toHaveBeenCalledTimes(3);
    });
    
    it('should handle case with no issues to fix', async () => {
      // Mock database response with no issues
      db.pool.query.mockResolvedValueOnce({
        rows: []
      });
      
      // Call the method
      const result = await fixService.fixTripMonitoring();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.details.fixedTrips).toHaveLength(0);
      expect(result.affectedRecords).toHaveLength(0);
      
      // Verify database calls - should only make the initial check
      expect(db.pool.query).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('logFixOperation', () => {
    it('should log fix operations to the database', async () => {
      // Mock database response
      db.pool.query.mockResolvedValueOnce({
        rows: [{ id: 'log_123' }]
      });
      
      // Call the method
      const result = await fixService.logFixOperation('shifts', 'success', 'Fixed 2 shift issues', {
        before: { issueCount: 2 },
        after: { issueCount: 0 }
      }, ['shift_123', 'shift_124']);
      
      // Assertions
      expect(result).toBe('log_123');
      
      // Verify database call
      expect(db.pool.query).toHaveBeenCalledTimes(1);
      expect(db.pool.query.mock.calls[0][0]).toContain('INSERT INTO automated_fix_logs');
    });
    
    it('should handle database errors when logging', async () => {
      // Mock database error
      db.pool.query.mockRejectedValueOnce(new Error('Database connection failed'));
      
      // Call the method and expect it to throw
      await expect(fixService.logFixOperation('shifts', 'success', 'Fixed 2 shift issues', {}, [])).rejects.toThrow('Database connection failed');
    });
  });
});