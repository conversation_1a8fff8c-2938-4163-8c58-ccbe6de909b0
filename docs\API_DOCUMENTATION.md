# API Documentation

This document provides comprehensive documentation for the Hauling QR Trip Management System API endpoints.

## 🔗 Base URL

- **Development**: `http://localhost:5000/api`
- **Production**: `https://yourdomain.com/api`

## 🔐 Authentication

The API uses JWT (JSON Web Token) authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Authentication Endpoints

#### POST /auth/login
Login with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "role": "driver",
    "name": "<PERSON>"
  }
}
```

#### POST /auth/logout
Logout current user (invalidates token).

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

## 📱 QR Scanner API

### POST /scanner/scan
Process QR code scan with enhanced validation and error handling.

**Request Body:**
```json
{
  "scan_type": "location", // or "truck"
  "scanned_data": "{\"type\":\"location\",\"id\":\"LOC-001\"}",
  "location_scan_data": {
    "id": "LOC-001",
    "name": "Warehouse A",
    "type": "location"
  }, // Required for truck scans
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "request_id": "scan_1721030400_abc123"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Location 'Warehouse A' scanned successfully",
  "data": {
    "location": {
      "id": "LOC-001",
      "code": "LOC-001",
      "name": "Warehouse A",
      "type": "loading",
      "coordinates": [40.7128, -74.0060]
    },
    "trip": {
      "id": 123,
      "status": "loading_start",
      "truck_number": "T001",
      "driver_name": "John Doe"
    }
  },
  "next_step": "scan_truck", // or "trip_complete", "await_approval"
  "processing_time_ms": 1250
}
```

**Validation Error Response (400 Bad Request):**
```json
{
  "success": false,
  "error": "Validation Error",
  "error_type": "validation",
  "message": "4-Phase Workflow Violation: Cannot start loading operation at unloading location 'Point B - Primary Dump Site'. Loading operations must be performed at loading-type locations only.",
  "details": {
    "type": "workflow_violation",
    "current_phase": "unloading_end",
    "required_action": "Must scan truck QR at loading location",
    "location_name": "Point B - Primary Dump Site",
    "location_type": "unloading",
    "violation_type": "location_type_mismatch",
    "next_steps": [
      "Navigate to an appropriate loading location",
      "Scan the truck QR code at the loading location",
      "Ensure the location type matches the required operation"
    ]
  },
  "processing_time_ms": 1250,
  "location_data": {
    "id": "LOC-002",
    "name": "Point B - Primary Dump Site",
    "type": "unloading"
  }
}
```

**System Error Response (500 Internal Server Error):**
```json
{
  "success": false,
  "error": "Scan Processing Error",
  "error_type": "system",
  "message": "Database connection failed during scan processing",
  "processing_time_ms": 1250,
  "location_data": {
    "id": "LOC-001",
    "name": "Warehouse A",
    "type": "loading"
  }
}
```

### Common QR Scanner Validation Scenarios

#### Location Type Validation
- **Loading operations** can only be performed at `loading` type locations
- **Unloading operations** can only be performed at `unloading` type locations
- **Checkpoint operations** can be performed at `checkpoint` type locations

#### 4-Phase Workflow Validation
- **Phase 1**: Loading Start - Must scan truck at loading location
- **Phase 2**: Loading End - Must complete loading at same location
- **Phase 3**: Unloading Start - Must scan truck at unloading location
- **Phase 4**: Unloading End - Must complete unloading at same location

#### Post-Completion Temporal Validation
- **Same Location**: 5-minute minimum gap before new trip at completion location
- **Different Location**: No temporal restriction for legitimate transitions
- **A→B→C Workflows**: Automatic detection and assignment creation

#### Assignment Role Validation
- **Truck Assignment**: Must match location requirements
- **Driver Assignment**: Must have active shift during operation
- **Location Assignment**: Must be included in truck's assignment route

## 🚛 Trip Management

### GET /trips
Get list of trips with optional filtering.

**Query Parameters:**
- `status` - Filter by trip status
- `driver_id` - Filter by driver ID
- `truck_id` - Filter by truck ID
- `date_from` - Start date filter (YYYY-MM-DD)
- `date_to` - End date filter (YYYY-MM-DD)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)

**Response:**
```json
{
  "success": true,
  "data": {
    "trips": [
      {
        "id": 1,
        "assignment_id": 123,
        "truck_id": "TK-001",
        "driver_id": "DR-001",
        "status": "in_progress",
        "pickup_location": "Warehouse A",
        "delivery_location": "Site B",
        "location_sequence": [
          {
            "location": "Warehouse A",
            "type": "pickup",
            "confirmed": true,
            "timestamp": "2025-07-15T08:00:00Z"
          }
        ],
        "workflow_type": "standard",
        "is_extended_trip": false,
        "created_at": "2025-07-15T07:30:00Z",
        "updated_at": "2025-07-15T08:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3
    }
  }
}
```

### GET /trips/:id
Get specific trip details.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "assignment_id": 123,
    "truck_id": "TK-001",
    "driver_id": "DR-001",
    "driver_name": "John Doe",
    "truck_number": "TK-001",
    "status": "loading_end",
    "pickup_location": "Warehouse A",
    "delivery_location": "Site B",
    "location_sequence": [
      {
        "location": "Warehouse A",
        "type": "pickup",
        "confirmed": true,
        "timestamp": "2025-07-15T08:00:00Z"
      },
      {
        "location": "Site B",
        "type": "delivery",
        "confirmed": false,
        "timestamp": null
      }
    ],
    "workflow_type": "standard",
    "is_extended_trip": false,
    "baseline_trip_id": null,
    "cycle_number": null,
    "timestamps": {
      "loading_start": "2025-07-15T08:00:00Z",
      "loading_end": "2025-07-15T08:30:00Z",
      "unloading_start": null,
      "unloading_end": null,
      "trip_completed": null
    },
    "created_at": "2025-07-15T07:30:00Z",
    "updated_at": "2025-07-15T08:30:00Z"
  }
}
```

### POST /trips/:id/scan
Process QR code scan for trip.

**Request Body:**
```json
{
  "qr_code": "LOC-001:1721030400:abc123hash",
  "location_type": "pickup", // or "delivery"
  "action": "start", // or "end"
  "latitude": 40.7128,
  "longitude": -74.0060,
  "timestamp": "2025-07-15T08:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "trip_id": 1,
    "status": "loading_start",
    "location": "Warehouse A",
    "action": "start",
    "timestamp": "2025-07-15T08:00:00Z",
    "next_action": "Complete loading and scan to finish",
    "workflow_detected": null
  }
}
```

### POST /trips/:id/extend
Create extended trip (A→B→C workflow).

**Request Body:**
```json
{
  "new_location": "Site C",
  "location_type": "pickup", // or "delivery"
  "reason": "Additional pickup required"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "extended_trip_id": 2,
    "baseline_trip_id": 1,
    "workflow_type": "extended",
    "new_assignment_id": 124,
    "route": "Site B → Site C"
  }
}
```

## 👥 Driver Management

### GET /drivers
Get list of drivers.

**Query Parameters:**
- `status` - Filter by status (active, inactive)
- `shift` - Filter by current shift
- `search` - Search by name or ID

## 🕐 Shift Management

### GET /shifts
Get list of driver shifts with enhanced status evaluation.

**Query Parameters:**
- `status` - Filter by shift status (scheduled, active, completed, cancelled)
- `driver_id` - Filter by driver ID
- `truck_id` - Filter by truck ID
- `shift_type` - Filter by shift type (day, night)
- `date_from` - Start date filter
- `date_to` - End date filter

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "driver_id": "DR-001",
      "truck_id": "TK-001",
      "shift_type": "night",
      "status": "active",
      "start_date": "2025-07-15",
      "end_date": "2025-07-16",
      "start_time": "20:00:00",
      "end_time": "06:00:00",
      "recurrence_pattern": "single",
      "is_overnight": true,
      "calculated_status": "active",
      "created_at": "2025-07-15T19:00:00Z",
      "updated_at": "2025-07-15T20:00:00Z"
    }
  ]
}
```

### POST /shifts/evaluate-status
Evaluate shift status using enhanced logic for overnight shifts.

**Request Body:**
```json
{
  "shift_id": 1,
  "reference_timestamp": "2025-07-16T02:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "shift_id": 1,
    "current_status": "active",
    "calculated_status": "active",
    "is_overnight": true,
    "is_within_date_range": true,
    "is_within_time_window": true,
    "evaluation_timestamp": "2025-07-16T02:00:00Z"
  }
}
```

### POST /shifts/auto-activate
Trigger automatic shift activation and completion using unified logic.

**Response:**
```json
{
  "success": true,
  "data": {
    "activated_count": 3,
    "completed_count": 2,
    "processed_at": "2025-07-16T08:00:00Z",
    "details": [
      {
        "shift_id": 1,
        "action": "activated",
        "driver_id": "DR-001",
        "shift_type": "day"
      },
      {
        "shift_id": 2,
        "action": "completed",
        "driver_id": "DR-002",
        "shift_type": "night"
      }
    ]
  }
}
```

### PUT /shifts/:id ✅ ENHANCED
Update existing shift with comprehensive field support. **Fixed validation error "recurrence_pattern is not allowed" as of July 18, 2025.**

**Request Body (All fields optional):**
```json
{
  // Basic shift information
  "truck_id": 2,
  "driver_id": 3,
  "shift_type": "day", // or "night"
  "status": "scheduled", // scheduled, active, completed, cancelled
  
  // Date and time fields - Perfect for extending shifts
  "start_date": "2025-07-20", // ISO date format
  "end_date": "2025-08-15",   // ISO date format - extend date range
  "shift_date": "2025-07-20", // Legacy support - single date
  "start_time": "07:00:00",   // HH:MM:SS format
  "end_time": "19:00:00",     // HH:MM:SS format
  
  // Advanced fields (now fully supported)
  "recurrence_pattern": "custom", // single, daily, weekly, weekdays, weekends, custom
  "display_type": "recurring",    // single, recurring
  "assignment_id": 123,           // Link to assignment
  
  // Notes and metadata
  "handover_notes": "Shift extended due to project requirements",
  "completion_notes": "Completed successfully",
  "cancellation_reason": "Driver unavailable",
  
  // System fields
  "previous_shift_id": 456,
  "handover_completed_at": "2025-07-20T19:00:00Z",
  "auto_created": false
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Shift updated successfully",
  "data": {
    "id": 1,
    "truck_id": 2,
    "driver_id": 3,
    "shift_type": "day",
    "start_date": "2025-07-20",
    "end_date": "2025-08-15",
    "start_time": "07:00:00",
    "end_time": "19:00:00",
    "status": "scheduled",
    "recurrence_pattern": "custom",
    "display_type": "recurring",
    "handover_notes": "Shift extended due to project requirements",
    "updated_at": "2025-07-18T10:30:00Z"
  }
}
```

**Validation Error Response (400 Bad Request):**
```json
{
  "success": false,
  "error": "Validation Error",
  "message": "start_time must be in HH:MM or HH:MM:SS format",
  "details": {
    "field": "start_time",
    "value": "25:00",
    "constraint": "Must be valid time in HH:MM or HH:MM:SS format"
  }
}
```

### Common Shift Editing Use Cases

#### Extending Date Range (Most Common)
```bash
PUT /api/shifts/123
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "start_date": "2025-07-20",
  "end_date": "2025-08-31",
  "recurrence_pattern": "custom"
}
```

#### Changing Time Schedule
```bash
PUT /api/shifts/123
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "start_time": "06:00:00",
  "end_time": "18:00:00",
  "shift_type": "day"
}
```

#### Reassigning Driver/Truck
```bash
PUT /api/shifts/123
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "driver_id": 5,
  "truck_id": 3,
  "handover_notes": "Reassigned due to driver availability"
}
```

#### Updating Shift Status
```bash
PUT /api/shifts/123
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "status": "cancelled",
  "cancellation_reason": "Equipment maintenance required"
}
```

### POST /shifts
Create new shift with enhanced validation and date range support.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "DR-001",
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+**********",
      "license_number": "DL123456",
      "status": "active",
      "current_shift": {
        "id": 1,
        "start_time": "08:00:00",
        "end_time": "16:00:00",
        "shift_type": "day"
      },
      "current_assignment": {
        "id": 123,
        "truck_id": "TK-001",
        "status": "in_progress"
      }
    }
  ]
}
```

### GET /drivers/:id
Get specific driver details.

### POST /drivers
Create new driver.

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "license_number": "DL789012",
  "hire_date": "2025-07-15"
}
```

### PUT /drivers/:id
Update driver information.

### DELETE /drivers/:id
Deactivate driver.

## 🚚 Truck Management

### GET /trucks
Get list of trucks.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "TK-001",
      "truck_number": "TK-001",
      "make": "Volvo",
      "model": "FH16",
      "year": 2022,
      "capacity": 40000,
      "status": "active",
      "current_location": "Warehouse A",
      "current_driver": {
        "id": "DR-001",
        "name": "John Doe"
      },
      "maintenance_due": "2025-08-15"
    }
  ]
}
```

### GET /trucks/:id
Get specific truck details.

### POST /trucks
Create new truck.

### PUT /trucks/:id
Update truck information.

## 📍 Location Management

### GET /locations
Get list of locations.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "LOC-001",
      "name": "Warehouse A",
      "address": "123 Industrial Blvd",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "location_type": "warehouse",
      "qr_code": "LOC-001:1721030400:abc123hash",
      "active": true
    }
  ]
}
```

### GET /locations/:id/qr
Generate QR code for location.

**Response:**
```json
{
  "success": true,
  "data": {
    "qr_code": "LOC-001:1721030400:abc123hash",
    "qr_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expires_at": "2025-07-16T08:00:00Z"
  }
}
```

## 📋 Assignment Management

### GET /assignments
Get list of assignments.

**Query Parameters:**
- `status` - Filter by status
- `driver_id` - Filter by driver
- `truck_id` - Filter by truck
- `date` - Filter by date

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "driver_id": "DR-001",
      "driver_name": "John Doe",
      "truck_id": "TK-001",
      "truck_number": "TK-001",
      "pickup_location": "Warehouse A",
      "delivery_location": "Site B",
      "status": "assigned",
      "shift_info": {
        "type": "day",
        "start_time": "08:00:00",
        "end_time": "16:00:00"
      },
      "trip_count": 0,
      "created_at": "2025-07-15T07:00:00Z"
    }
  ]
}
```

### POST /assignments
Create new assignment.

**Request Body:**
```json
{
  "driver_id": "DR-001",
  "truck_id": "TK-001",
  "pickup_location": "Warehouse A",
  "delivery_location": "Site B",
  "scheduled_date": "2025-07-15",
  "shift_id": 1,
  "priority": "normal"
}
```

### PUT /assignments/:id
Update assignment.

### DELETE /assignments/:id
Cancel assignment.

## 🏥 System Health Monitoring

### GET /system-health/status
Get current health status for all system modules.

**Response:**
```json
{
  "success": true,
  "data": {
    "shifts": {
      "status": "operational",
      "issues": [],
      "lastCheck": "2025-07-16T10:30:00Z",
      "metrics": {
        "totalShifts": 4,
        "activeShifts": 2,
        "scheduledShifts": 2,
        "completedShifts": 0,
        "statusMismatches": 0
      }
    },
    "assignments": {
      "status": "warning",
      "issues": [
        {
          "id": "assign-001",
          "type": "display_mismatch",
          "severity": "medium",
          "description": "Assignment display showing 'No Active Shift' during active hours",
          "affectedRecords": ["assignment_123"],
          "autoFixable": true
        }
      ],
      "lastCheck": "2025-07-16T10:30:00Z",
      "metrics": {
        "totalAssignments": 8,
        "activeAssignments": 6,
        "displayIssues": 1
      }
    },
    "trips": {
      "status": "operational",
      "issues": [],
      "lastCheck": "2025-07-16T10:30:00Z",
      "metrics": {
        "activeTrips": 12,
        "completedTrips": 45,
        "workflowIssues": 0
      }
    },
    "overall": {
      "status": "warning",
      "criticalIssues": 0,
      "warningIssues": 1,
      "lastFullCheck": "2025-07-16T10:30:00Z"
    }
  }
}
```

### POST /system-health/fix-shifts
Execute automated fixes for shift management issues.

**Response:**
```json
{
  "success": true,
  "data": {
    "fixesApplied": [
      {
        "type": "status_update",
        "description": "Executed schedule_auto_activation() function",
        "affectedRecords": ["shift_123", "shift_124"],
        "timestamp": "2025-07-16T10:31:00Z"
      }
    ],
    "summary": {
      "totalFixes": 1,
      "successfulFixes": 1,
      "failedFixes": 0,
      "shiftsActivated": 2,
      "shiftsCompleted": 1
    },
    "verification": {
      "success": true,
      "statusAfterFix": "operational"
    }
  }
}
```

### POST /system-health/fix-assignments
Synchronize assignment displays with active shifts.

**Response:**
```json
{
  "success": true,
  "data": {
    "fixesApplied": [
      {
        "type": "display_sync",
        "description": "Synchronized assignment display with active shift status",
        "affectedRecords": ["assignment_123"],
        "timestamp": "2025-07-16T10:32:00Z"
      }
    ],
    "summary": {
      "totalFixes": 1,
      "assignmentsSynced": 1,
      "displayIssuesResolved": 1
    }
  }
}
```

### POST /system-health/fix-trips
Resolve trip workflow integrity issues.

**Response:**
```json
{
  "success": true,
  "data": {
    "fixesApplied": [
      {
        "type": "workflow_correction",
        "description": "Corrected trip status transition",
        "affectedRecords": ["trip_456"],
        "timestamp": "2025-07-16T10:33:00Z"
      }
    ],
    "summary": {
      "totalFixes": 1,
      "workflowIssuesResolved": 1,
      "tripsUpdated": 1
    }
  }
}
```

## 📋 Task Management

### GET /tasks
Get list of maintenance tasks and recommendations.

**Query Parameters:**
- `status` - Filter by task status (pending, in_progress, completed, failed)
- `type` - Filter by task type (maintenance, cleanup, monitoring, optimization)
- `priority` - Filter by priority (low, medium, high, critical)

**Response:**
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": "task-001",
        "type": "maintenance",
        "priority": "high",
        "status": "pending",
        "title": "Fix shift status inconsistencies",
        "description": "Multiple shifts showing incorrect status during transition hours",
        "createdAt": "2025-07-16T09:00:00Z",
        "scheduledFor": "2025-07-16T11:00:00Z",
        "estimatedDuration": 300,
        "autoExecutable": true,
        "metadata": {
          "affectedShifts": ["shift_123", "shift_124"],
          "module": "shifts"
        }
      }
    ],
    "recommendations": [
      {
        "id": "rec-001",
        "type": "optimization",
        "priority": "medium",
        "title": "Schedule regular cleanup operations",
        "description": "Consider scheduling weekly cleanup tasks to maintain system performance",
        "actionable": true,
        "estimatedImpact": "Improved system performance and reduced maintenance overhead",
        "implementationEffort": "low"
      }
    ],
    "summary": {
      "totalTasks": 5,
      "pendingTasks": 3,
      "completedTasks": 2,
      "criticalTasks": 1
    }
  }
}
```

### POST /tasks
Create new maintenance task.

**Request Body:**
```json
{
  "type": "maintenance",
  "priority": "high",
  "title": "Database optimization",
  "description": "Optimize database queries for better performance",
  "scheduledFor": "2025-07-16T15:00:00Z",
  "estimatedDuration": 1800,
  "autoExecutable": false,
  "metadata": {
    "module": "database",
    "queries": ["SELECT * FROM trips", "SELECT * FROM assignments"]
  }
}
```

### PUT /tasks/:id
Update task status or details.

### DELETE /tasks/:id
Remove completed or cancelled task.

## 🧹 Cleanup Management

### POST /cleanup/analyze
Analyze codebase for unused functions and optimization opportunities.

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis-001",
    "status": "completed",
    "timestamp": "2025-07-16T10:35:00Z",
    "results": {
      "serverFiles": {
        "totalFiles": 45,
        "totalFunctions": 234,
        "unusedFunctions": 12,
        "criticalFunctions": 222,
        "details": [
          {
            "file": "server/utils/legacy-helper.js",
            "unusedFunctions": ["oldHelperFunction", "deprecatedUtil"],
            "safeToRemove": true,
            "lastUsed": "2024-12-01T00:00:00Z"
          }
        ]
      },
      "scriptFiles": {
        "totalFiles": 28,
        "totalFunctions": 156,
        "unusedFunctions": 8,
        "criticalFunctions": 148,
        "details": []
      }
    },
    "recommendations": [
      "Remove 12 unused functions from server files to reduce bundle size",
      "Consider refactoring 3 large functions for better maintainability",
      "Archive 2 legacy utility files that are no longer needed"
    ],
    "estimatedImpact": {
      "linesReduced": 145,
      "filesAffected": 8,
      "performanceGain": "5-10%"
    }
  }
}
```

### POST /cleanup/execute
Execute cleanup operations based on analysis.

**Request Body:**
```json
{
  "analysisId": "analysis-001",
  "options": {
    "createBackup": true,
    "dryRun": false,
    "preserveCritical": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "cleanupId": "cleanup-001",
    "analysisId": "analysis-001",
    "timestamp": "2025-07-16T10:40:00Z",
    "summary": {
      "filesModified": 8,
      "functionsRemoved": 12,
      "linesRemoved": 145,
      "backupCreated": true,
      "backupId": "backup-001"
    },
    "details": [
      {
        "file": "server/utils/legacy-helper.js",
        "functionsRemoved": ["oldHelperFunction", "deprecatedUtil"],
        "linesRemoved": 25
      }
    ],
    "rollbackAvailable": true,
    "verificationStatus": "passed"
  }
}
```

### POST /cleanup/rollback
Rollback cleanup operations if issues are detected.

**Request Body:**
```json
{
  "cleanupId": "cleanup-001"
}
```

## 📊 Analytics & Reporting

### GET /analytics/dashboard
Get dashboard analytics.

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "active_trips": 12,
      "completed_trips_today": 45,
      "active_drivers": 8,
      "active_trucks": 10
    },
    "performance": {
      "avg_trip_duration": 120, // minutes
      "on_time_percentage": 85.5,
      "efficiency_score": 92.3
    },
    "recent_activities": [
      {
        "type": "trip_completed",
        "message": "Trip TRP-001 completed by John Doe",
        "timestamp": "2025-07-15T08:30:00Z"
      }
    ]
  }
}
```

### GET /analytics/trips
Get trip analytics.

**Query Parameters:**
- `period` - Time period (day, week, month, year)
- `date_from` - Start date
- `date_to` - End date
- `group_by` - Group by (driver, truck, location)

### GET /analytics/performance
Get performance metrics.

### GET /reports/trips
Generate trip report.

**Query Parameters:**
- `format` - Report format (json, csv, pdf)
- `date_from` - Start date
- `date_to` - End date
- `filters` - Additional filters

## 🔄 Real-time Updates (WebSocket)

### Connection
Connect to WebSocket at: `ws://localhost:5000` or `wss://yourdomain.com`

### Authentication
Send authentication message after connection:
```json
{
  "type": "auth",
  "token": "your_jwt_token"
}
```

### Message Types

#### Trip Updates
```json
{
  "type": "trip_update",
  "data": {
    "trip_id": 1,
    "status": "loading_end",
    "timestamp": "2025-07-15T08:30:00Z",
    "location": "Warehouse A"
  }
}
```

#### Workflow Notifications
```json
{
  "type": "trip_extended",
  "data": {
    "baseline_trip_id": 1,
    "extended_trip_id": 2,
    "driver_name": "John Doe",
    "route": "Site B → Site C"
  }
}
```

#### System Alerts
```json
{
  "type": "system_alert",
  "data": {
    "level": "warning",
    "message": "Driver DR-001 has been idle for 30 minutes",
    "timestamp": "2025-07-15T09:00:00Z"
  }
}
```

## ⚠️ Enhanced Error Handling

The API features comprehensive error handling that distinguishes between validation errors (business rule violations) and system errors, providing clear, actionable guidance.

### Error Response Format

#### Validation Error (400 Bad Request)
Business rule violations with actionable guidance:
```json
{
  "success": false,
  "error": "Validation Error",
  "error_type": "validation",
  "message": "4-Phase Workflow Violation: Cannot perform loading operation at unloading location",
  "details": {
    "type": "workflow_violation",
    "current_phase": "unloading_end",
    "required_action": "Must scan truck QR at loading location",
    "location_name": "Point B - Primary Dump Site",
    "location_type": "unloading",
    "field": "workflow_state",
    "code": "WORKFLOW_VIOLATION",
    "suggestion": "Navigate to the appropriate loading location to continue the workflow"
  },
  "processing_time_ms": 1250,
  "location_data": {
    "id": "LOC-002",
    "name": "Point B - Primary Dump Site",
    "type": "unloading"
  }
}
```

#### System Error (500 Internal Server Error)
Actual system failures requiring technical intervention:
```json
{
  "success": false,
  "error": "Scan Processing Error",
  "error_type": "system",
  "message": "Database connection failed",
  "processing_time_ms": 1250,
  "location_data": null
}
```

#### Legacy Error Format (Deprecated)
For backward compatibility, some endpoints may still return:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

### Error Classification

#### Validation Errors (400 Bad Request)
- **Workflow Violations**: 4-phase workflow rule violations
- **Location Type Mismatches**: Loading operations at unloading locations
- **Assignment Role Conflicts**: Truck assignments not matching location requirements
- **Temporal Validation**: Post-completion scanning restrictions
- **Input Validation**: Invalid QR codes, missing required fields

#### System Errors (500 Internal Server Error)
- **Database Connection Issues**: Connection pool exhaustion, query timeouts
- **Transaction Failures**: Database transaction rollback errors
- **Authentication Failures**: JWT token validation errors
- **External Service Errors**: Third-party API failures

### Common Error Codes

| Code | Description | HTTP Status | Error Type |
|------|-------------|-------------|------------|
| `WORKFLOW_VIOLATION` | 4-phase workflow rule violation | 400 | validation |
| `LOCATION_TYPE_MISMATCH` | Invalid location type for operation | 400 | validation |
| `ASSIGNMENT_ROLE_CONFLICT` | Truck assignment doesn't match location | 400 | validation |
| `TEMPORAL_VALIDATION_ERROR` | Post-completion scanning too soon | 400 | validation |
| `QR_VALIDATION_ERROR` | Invalid QR code format or data | 400 | validation |
| `UNAUTHORIZED` | Authentication required | 401 | system |
| `FORBIDDEN` | Insufficient permissions | 403 | system |
| `NOT_FOUND` | Resource not found | 404 | system |
| `CONFLICT` | Resource conflict | 409 | system |
| `RATE_LIMIT_EXCEEDED` | Too many requests | 429 | system |
| `DATABASE_ERROR` | Database connection/query failure | 500 | system |
| `TRANSACTION_ERROR` | Database transaction failure | 500 | system |
| `INTERNAL_ERROR` | Unexpected server error | 500 | system |

### Error Handling Best Practices

#### Client-Side Handling
```javascript
try {
  const response = await scannerAPI.processScan(scanRequest);
  // Handle success
} catch (error) {
  const errorData = error.response?.data;
  const isValidationError = errorData?.error_type === 'validation';
  
  if (isValidationError) {
    // Handle validation errors with user guidance
    if (errorData.message?.includes('4-Phase Workflow Violation')) {
      // Show workflow-specific guidance with next steps
      showWorkflowError(errorData);
    } else {
      // Show general validation error
      showValidationError(errorData);
    }
  } else {
    // Handle system errors
    showSystemError(errorData);
  }
}
```

#### Server-Side Error Creation
```javascript
const ValidationError = require('../utils/ValidationError');

// Workflow violation
throw new ValidationError(
  '4-Phase Workflow Violation: Cannot perform loading operation at unloading location',
  {
    type: 'workflow_violation',
    current_phase: 'unloading_end',
    required_action: 'Must scan truck QR at loading location',
    location_name: location.name,
    location_type: location.type,
    field: 'workflow_state',
    code: 'WORKFLOW_VIOLATION',
    suggestion: 'Navigate to the appropriate loading location to continue the workflow'
  }
);

// Field validation error
throw ValidationError.forField(
  'truck_id',
  'Truck not found or inactive',
  truckId,
  'Verify the truck number and ensure it is active'
);

// Business rule violation
throw ValidationError.forBusinessRule(
  'Post-completion temporal validation',
  {
    truck_id: truckId,
    location_id: locationId,
    minutes_since_completion: minutesSinceCompletion,
    minimum_required_minutes: 5
  }
);
```

### Error Response Headers

All error responses include additional headers for debugging:

```
X-Error-Type: validation | system
X-Error-Code: WORKFLOW_VIOLATION
X-Processing-Time: 1250
X-Request-ID: scan_1721030400_abc123
```

## 🔒 Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Default**: 100 requests per 15 minutes per IP
- **Authentication**: 5 login attempts per 15 minutes per IP
- **QR Scanning**: 60 scans per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1721031000
```

## 📝 Request/Response Examples

### Complete Trip Workflow Example

1. **Get Assignment**
   ```bash
   GET /api/assignments/123
   ```

2. **Start Loading**
   ```bash
   POST /api/trips/1/scan
   {
     "qr_code": "LOC-001:1721030400:abc123hash",
     "location_type": "pickup",
     "action": "start"
   }
   ```

3. **Complete Loading**
   ```bash
   POST /api/trips/1/scan
   {
     "qr_code": "LOC-001:1721030400:abc123hash",
     "location_type": "pickup",
     "action": "end"
   }
   ```

4. **Start Unloading**
   ```bash
   POST /api/trips/1/scan
   {
     "qr_code": "LOC-002:1721034000:def456hash",
     "location_type": "delivery",
     "action": "start"
   }
   ```

5. **Complete Trip**
   ```bash
   POST /api/trips/1/scan
   {
     "qr_code": "LOC-002:1721034000:def456hash",
     "location_type": "delivery",
     "action": "end"
   }
   ```

## 🧪 Testing the API

### Using cURL

```bash
# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Get trips (with token)
curl -X GET http://localhost:5000/api/trips \
  -H "Authorization: Bearer YOUR_TOKEN"

# Scan QR code
curl -X POST http://localhost:5000/api/trips/1/scan \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"qr_code":"LOC-001:1721030400:abc123hash","location_type":"pickup","action":"start"}'
```

### Using Postman

1. Import the API collection (if available)
2. Set up environment variables:
   - `base_url`: `http://localhost:5000/api`
   - `token`: Your JWT token
3. Use `{{base_url}}` and `{{token}}` in requests

## 📚 Additional Resources

- **[Development Setup Guide](DEVELOPMENT_SETUP.md)** - Setting up the development environment
- **[Multi-Location Workflow Guide](MULTI_LOCATION_WORKFLOW_IMPLEMENTATION.md)** - Advanced workflow features
- **[Troubleshooting Guide](ASSIGNMENT_SHIFT_FIX_GUIDE.md)** - Common issues and solutions

---

For questions or issues with the API, please refer to the main documentation or create an issue in the project repository.