-- Migration: Fix approvals table schema to match server code expectations
-- Date: 2025-01-03
-- Purpose: Fix 500 errors caused by missing columns in approvals table

-- Add missing columns to approvals table
DO $$ 
BEGIN 
    -- Add severity column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'severity') THEN
        ALTER TABLE approvals ADD COLUMN severity VARCHAR(20) DEFAULT 'medium' 
        CHECK (severity IN ('low', 'medium', 'high', 'critical'));
        
        -- Set default severity for existing records
        UPDATE approvals SET severity = 'medium' WHERE severity IS NULL;
    END IF;
    
    -- Add reported_by column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'reported_by') THEN
        ALTER TABLE approvals ADD COLUMN reported_by INTEGER REFERENCES users(id);
    END IF;
    
    -- Rename description to exception_description if needed
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'approvals' AND column_name = 'description') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'approvals' AND column_name = 'exception_description') THEN
        ALTER TABLE approvals RENAME COLUMN description TO exception_description;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_approvals_severity ON approvals(severity);
CREATE INDEX IF NOT EXISTS idx_approvals_reported_by ON approvals(reported_by);
