# Cloudflare Configuration Guide for truckhaul.top

This guide provides step-by-step instructions for configuring Cloudflare with your Hauling QR Trip Management System deployment on the domain `truckhaul.top`.

## Prerequisites

- A Cloudflare account
- Domain `truckhaul.top` added to your Cloudflare account
- Nameservers updated to use Cloudflare's nameservers
- Deployment script already executed on your Ubuntu 24.04 VPS

## Step 1: Configure DNS Records

1. Log in to your Cloudflare dashboard
2. Select the domain `truckhaul.top`
3. Go to the DNS tab
4. Add or update the following records:

| Type | Name | Content | Proxy Status |
|------|------|---------|-------------|
| A | @ | [Your Server IP] | Proxied (Orange Cloud) |
| A | www | [Your Server IP] | Proxied (Orange Cloud) |

## Step 2: Configure SSL/TLS Settings

1. Go to the SSL/TLS tab
2. Select the "Overview" section
3. Set the encryption mode to "Full"
   - This ensures traffic is encrypted between Cloudflare and your origin server
   - Your server is already configured with a self-signed certificate for this purpose

## Step 3: Enable Always Use HTTPS

1. Go to the SSL/TLS tab
2. Select the "Edge Certificates" section
3. Enable "Always Use HTTPS"
   - This automatically redirects all HTTP requests to HTTPS

## Step 4: Configure Page Rules (Optional)

For optimal performance, consider adding these page rules:

1. Go to the Page Rules tab
2. Create a new page rule for `truckhaul.top/*`
3. Add the following settings:
   - Cache Level: Standard
   - Browser Cache TTL: 4 hours
   - Always Online: On

## Step 5: Optimize Performance

1. Go to the Speed tab
2. Enable Auto Minify for HTML, CSS, and JavaScript
3. Enable Brotli compression
4. Enable Early Hints
5. Set Browser Cache TTL to 4 hours

## Step 6: Enhance Security (Recommended)

1. Go to the Security tab
2. Set Security Level to "Medium"
3. Enable Bot Fight Mode
4. Enable Browser Integrity Check
5. Consider enabling Web Application Firewall (WAF) for additional protection

## Step 7: Verify Configuration

1. Visit `https://truckhaul.top` in your browser
2. Verify that the site loads correctly with HTTPS
3. Check that the Cloudflare SSL certificate is being used (click the padlock icon)
4. Verify that your application is functioning properly

## Troubleshooting

### SSL Issues

If you encounter SSL-related issues:

1. Verify that your server's self-signed certificate is properly configured
2. Check that Cloudflare's SSL mode is set to "Full" (not "Full (Strict)")
3. Ensure your Nginx configuration is correctly set up for SSL

### DNS Issues

If your domain isn't resolving correctly:

1. Verify that your nameservers are correctly set to Cloudflare's nameservers
2. Check that your A records are correctly pointing to your server's IP address
3. Ensure the proxy status is enabled (orange cloud) for your records

### Application Issues

If your application isn't functioning correctly:

1. Check your server's Nginx and application logs
2. Verify that Cloudflare's proxy isn't interfering with WebSocket connections
3. Ensure your application is correctly configured to work with HTTPS

## Additional Resources

- [Cloudflare SSL Documentation](https://developers.cloudflare.com/ssl/)
- [Cloudflare DNS Documentation](https://developers.cloudflare.com/dns/)
- [Cloudflare Performance Optimization](https://developers.cloudflare.com/performance/)