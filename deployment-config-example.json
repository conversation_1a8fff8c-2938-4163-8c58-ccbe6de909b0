{"domainName": "truckhaul.top", "sslMode": "cloudflare", "dbPassword": "", "jwtSecret": "", "adminUsername": "admin", "adminPassword": "", "adminEmail": "<EMAIL>", "repoUrl": "https://github.com/your-org/hauling-qr-trip-system.git", "repoBranch": "main", "environment": "production", "monitoringEnabled": true, "backupEnabled": true, "backupRetentionDays": 7, "advanced": {"nodeVersion": "18", "postgresVersion": "15", "appUser": "hauling", "appDir": "/var/www/hauling-qr-system", "pm2Instances": "max", "maxMemoryRestart": "2G", "nodeMaxOldSpace": 2048, "fail2banBantime": 3600, "fail2banMaxretry": 5, "ufwEnable": true, "sslCertPath": "/etc/nginx/ssl/certificate.crt", "sslKeyPath": "/etc/nginx/ssl/private.key", "sslCountry": "US", "sslState": "State", "sslCity": "City", "sslOrg": "Organization", "nginxRateLimitApi": "20r/s", "nginxRateLimitAuth": "5r/m", "nginxRateLimitGeneral": "10r/s", "dbSharedBuffers": "256MB", "dbEffectiveCacheSize": "1GB", "dbWorkMem": "4MB", "dbMaxConnections": 100, "healthCheckInterval": "*/5 * * * *", "performanceCheckInterval": "*/10 * * * *", "reportGenerationTime": "0 6 * * *", "fullBackupSchedule": "0 3 * * 0", "backupCompression": true, "logLevel": "info", "logRotationSize": "100M", "logRetentionDays": 52, "smtpHost": "smtp.gmail.com", "smtpPort": 587, "smtpUser": "<EMAIL>", "smtpPassword": "", "alertEmail": "<EMAIL>"}, "cloudflare": {"sslMode": "full", "apiToken": "", "zoneId": "", "minifyHtml": true, "minifyCss": true, "minifyJs": true, "brotli": true, "cacheLevel": "standard", "browserCacheTtl": 14400}}