-- Migration: Ensure driver_shifts table exists
-- Purpose: Create the driver_shifts table if it doesn't exist

-- Check if the table exists and create it if it doesn't
DO $$
BEGIN
    -- Check if the table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'driver_shifts'
    ) THEN
        -- Create the table
        CREATE TABLE driver_shifts (
            id SERIAL PRIMARY KEY,
            driver_id INTEGER,
            truck_id INTEGER,
            shift_type VARCHAR(10) NOT NULL CHECK (shift_type IN ('day', 'night')),
            status VARCHAR(20) NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'active', 'completed', 'cancelled')),
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            completion_notes TEXT,
            cancellation_reason TEXT
        );
        
        -- <PERSON><PERSON> indexes
        CREATE INDEX idx_driver_shifts_driver_id ON driver_shifts(driver_id);
        CREATE INDEX idx_driver_shifts_truck_id ON driver_shifts(truck_id);
        CREATE INDEX idx_driver_shifts_status ON driver_shifts(status);
        CREATE INDEX idx_driver_shifts_dates ON driver_shifts(start_date, end_date);
        
        -- Add comments
        COMMENT ON TABLE driver_shifts IS 'Driver shift schedules and status tracking';
        COMMENT ON COLUMN driver_shifts.shift_type IS 'Type of shift (day or night)';
        COMMENT ON COLUMN driver_shifts.status IS 'Current status of the shift (scheduled, active, completed, cancelled)';
        
        RAISE NOTICE 'Created driver_shifts table';
    ELSE
        -- Check if the completion_notes and cancellation_reason columns exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'driver_shifts'
            AND column_name = 'completion_notes'
        ) THEN
            -- Add completion_notes column
            ALTER TABLE driver_shifts ADD COLUMN completion_notes TEXT;
            RAISE NOTICE 'Added completion_notes column to driver_shifts table';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'driver_shifts'
            AND column_name = 'cancellation_reason'
        ) THEN
            -- Add cancellation_reason column
            ALTER TABLE driver_shifts ADD COLUMN cancellation_reason TEXT;
            RAISE NOTICE 'Added cancellation_reason column to driver_shifts table';
        END IF;
        
        RAISE NOTICE 'driver_shifts table already exists';
    END IF;
    
    -- Check if the shift_status type exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_type 
        WHERE typname = 'shift_status'
    ) THEN
        -- Create the shift_status type
        CREATE TYPE shift_status AS ENUM ('scheduled', 'active', 'completed', 'cancelled');
        RAISE NOTICE 'Created shift_status type';
    ELSE
        RAISE NOTICE 'shift_status type already exists';
    END IF;
END;
$$;