-- ============================================================================
-- MIGRATION TRACKER AND STATE MANAGEMENT SYSTEM
-- Hauling QR Trip Management System
-- ============================================================================
--
-- This system manages the consolidation of historical migrations (001-064)
-- and provides intelligent state detection for both fresh and existing databases.
--
-- Usage Scenarios:
-- 1. Fresh Database: Run consolidated migration directly
-- 2. Existing Database: Mark historical migrations as consolidated
-- 3. Partial Migration: Handle databases with some but not all migrations
--
-- ============================================================================

-- ============================================================================
-- MIGRATION STATE DETECTION FUNCTIONS
-- ============================================================================

-- Function to detect if database is fresh (no existing tables)
CREATE OR REPLACE FUNCTION is_fresh_database() RETURNS BOOLEAN
LANGUAGE plpgsql AS $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        AND table_name NOT IN ('migrations', 'migration_log');
    
    RETURN table_count = 0;
END;
$$;

-- Function to check if migrations table exists
CREATE OR REPLACE FUNCTION migrations_table_exists() RETURNS BOOLEAN
LANGUAGE plpgsql AS $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
            AND table_name = 'migrations'
    ) INTO table_exists;
    
    RETURN table_exists;
END;
$$;

-- Function to get current migration state
CREATE OR REPLACE FUNCTION get_migration_state() 
RETURNS TABLE(
    scenario TEXT,
    description TEXT,
    action_required TEXT,
    migration_count INTEGER,
    last_migration TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    is_fresh BOOLEAN;
    has_migrations_table BOOLEAN;
    migration_count INTEGER := 0;
    last_migration TEXT := NULL;
BEGIN
    is_fresh := is_fresh_database();
    has_migrations_table := migrations_table_exists();
    
    IF has_migrations_table THEN
        SELECT COUNT(*), MAX(filename) INTO migration_count, last_migration
        FROM migrations;
    END IF;
    
    IF is_fresh THEN
        scenario := 'FRESH_DATABASE';
        description := 'No existing tables found. Clean installation.';
        action_required := 'Run consolidated migration directly';
        RETURN NEXT;
    ELSIF NOT has_migrations_table THEN
        scenario := 'LEGACY_DATABASE';
        description := 'Existing tables but no migration tracking.';
        action_required := 'Initialize migration tracking and mark as consolidated';
        RETURN NEXT;
    ELSIF migration_count = 0 THEN
        scenario := 'EMPTY_MIGRATIONS';
        description := 'Migration table exists but no migrations recorded.';
        action_required := 'Mark existing schema as consolidated';
        RETURN NEXT;
    ELSIF migration_count >= 64 THEN
        scenario := 'FULLY_MIGRATED';
        description := 'All historical migrations completed.';
        action_required := 'Mark migrations as consolidated';
        RETURN NEXT;
    ELSE
        scenario := 'PARTIAL_MIGRATIONS';
        description := 'Some migrations completed (' || migration_count || '/64).';
        action_required := 'Complete remaining migrations or consolidate';
        RETURN NEXT;
    END IF;
END;
$$;

-- ============================================================================
-- CONSOLIDATION MANAGEMENT FUNCTIONS
-- ============================================================================

-- Function to mark historical migrations as consolidated
CREATE OR REPLACE FUNCTION mark_migrations_as_consolidated() RETURNS TEXT
LANGUAGE plpgsql AS $$
DECLARE
    result_message TEXT;
    migration_count INTEGER := 0;
    i INTEGER;
BEGIN
    -- Ensure migrations table exists
    IF NOT migrations_table_exists() THEN
        CREATE TABLE migrations (
            id SERIAL PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            executed_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE migration_log (
            id SERIAL PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL,
            executed_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
            description TEXT
        );
    END IF;
    
    -- Mark all historical migrations (001-064) as consolidated
    FOR i IN 1..64 LOOP
        INSERT INTO migrations (filename, executed_at)
        VALUES (
            LPAD(i::TEXT, 3, '0') || '_consolidated_historical_migration.sql',
            CURRENT_TIMESTAMP
        )
        ON CONFLICT DO NOTHING;
        
        GET DIAGNOSTICS migration_count = ROW_COUNT;
        
        IF migration_count > 0 THEN
            INSERT INTO migration_log (migration_name, description)
            VALUES (
                'Migration ' || LPAD(i::TEXT, 3, '0'),
                'Historical migration marked as consolidated'
            );
        END IF;
    END LOOP;
    
    -- Add consolidation marker
    INSERT INTO migrations (filename, executed_at)
    VALUES ('001_consolidated_schema.sql', CURRENT_TIMESTAMP)
    ON CONFLICT DO NOTHING;
    
    INSERT INTO migration_log (migration_name, description)
    VALUES (
        'Schema Consolidation v1.0',
        'All historical migrations (001-064) consolidated into single schema'
    );
    
    result_message := 'Successfully marked 64 historical migrations as consolidated. ' ||
                     'Database is now ready for future migrations starting from 065.';
    
    RETURN result_message;
END;
$$;

-- Function to validate schema completeness after consolidation
CREATE OR REPLACE FUNCTION validate_consolidated_schema() 
RETURNS TABLE(
    component TEXT,
    expected_count INTEGER,
    actual_count INTEGER,
    status TEXT,
    details TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    trigger_count INTEGER;
    view_count INTEGER;
    matview_count INTEGER;
    enum_count INTEGER;
BEGIN
    -- Count tables
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    
    component := 'Tables';
    expected_count := 17;
    actual_count := table_count;
    status := CASE WHEN table_count >= 17 THEN 'PASS' ELSE 'FAIL' END;
    details := 'Core operational tables';
    RETURN NEXT;
    
    -- Count functions
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines 
    WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';
    
    component := 'Functions';
    expected_count := 57;
    actual_count := function_count;
    status := CASE WHEN function_count >= 57 THEN 'PASS' ELSE 'FAIL' END;
    details := 'Business logic and automation functions';
    RETURN NEXT;
    
    -- Count triggers
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public';
    
    component := 'Triggers';
    expected_count := 13;
    actual_count := trigger_count;
    status := CASE WHEN trigger_count >= 13 THEN 'PASS' ELSE 'FAIL' END;
    details := 'Automated data management triggers';
    RETURN NEXT;
    
    -- Count views
    SELECT COUNT(*) INTO view_count
    FROM information_schema.views 
    WHERE table_schema = 'public';
    
    component := 'Views';
    expected_count := 6;
    actual_count := view_count;
    status := CASE WHEN view_count >= 6 THEN 'PASS' ELSE 'FAIL' END;
    details := 'Regular views for data access';
    RETURN NEXT;
    
    -- Count materialized views
    SELECT COUNT(*) INTO matview_count
    FROM pg_matviews 
    WHERE schemaname = 'public';
    
    component := 'Materialized Views';
    expected_count := 4;
    actual_count := matview_count;
    status := CASE WHEN matview_count >= 4 THEN 'PASS' ELSE 'FAIL' END;
    details := 'Performance-optimized analytics views';
    RETURN NEXT;
    
    -- Count enums
    SELECT COUNT(DISTINCT t.typname) INTO enum_count
    FROM pg_type t 
    JOIN pg_enum e ON t.oid = e.enumtypid;
    
    component := 'Enum Types';
    expected_count := 11;
    actual_count := enum_count;
    status := CASE WHEN enum_count >= 11 THEN 'PASS' ELSE 'FAIL' END;
    details := 'Type safety enumerations';
    RETURN NEXT;
END;
$$;

-- ============================================================================
-- MIGRATION EXECUTION FUNCTIONS
-- ============================================================================

-- Function to execute consolidation based on database state
CREATE OR REPLACE FUNCTION execute_consolidation() RETURNS TEXT
LANGUAGE plpgsql AS $$
DECLARE
    state_info RECORD;
    result_message TEXT;
BEGIN
    -- Get current database state
    SELECT * INTO state_info FROM get_migration_state() LIMIT 1;
    
    CASE state_info.scenario
        WHEN 'FRESH_DATABASE' THEN
            result_message := 'Fresh database detected. Please run 001_consolidated_schema.sql manually for complete setup.';
            
        WHEN 'LEGACY_DATABASE' THEN
            PERFORM mark_migrations_as_consolidated();
            result_message := 'Legacy database updated. Migration tracking initialized and historical migrations marked as consolidated.';
            
        WHEN 'EMPTY_MIGRATIONS' THEN
            PERFORM mark_migrations_as_consolidated();
            result_message := 'Migration tracking updated. Existing schema marked as consolidated.';
            
        WHEN 'FULLY_MIGRATED' THEN
            PERFORM mark_migrations_as_consolidated();
            result_message := 'Fully migrated database consolidated. All historical migrations marked as consolidated.';
            
        WHEN 'PARTIAL_MIGRATIONS' THEN
            result_message := 'Partial migration state detected. Please complete remaining migrations or contact administrator for manual consolidation.';
            
        ELSE
            result_message := 'Unknown database state. Manual intervention required.';
    END CASE;
    
    RETURN result_message;
END;
$$;

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to get consolidation status
CREATE OR REPLACE FUNCTION get_consolidation_status() 
RETURNS TABLE(
    is_consolidated BOOLEAN,
    consolidation_date TIMESTAMP,
    total_migrations INTEGER,
    schema_version TEXT,
    next_migration_number INTEGER
)
LANGUAGE plpgsql AS $$
DECLARE
    consolidated_marker_exists BOOLEAN;
    consolidation_timestamp TIMESTAMP;
    migration_count INTEGER;
BEGIN
    -- Check if consolidation marker exists
    SELECT EXISTS (
        SELECT 1 FROM migrations 
        WHERE filename = '001_consolidated_schema.sql'
    ) INTO consolidated_marker_exists;
    
    IF consolidated_marker_exists THEN
        SELECT executed_at INTO consolidation_timestamp
        FROM migrations 
        WHERE filename = '001_consolidated_schema.sql';
        
        SELECT COUNT(*) INTO migration_count FROM migrations;
        
        is_consolidated := TRUE;
        consolidation_date := consolidation_timestamp;
        total_migrations := migration_count;
        schema_version := 'v4.0-consolidated';
        next_migration_number := 65;
    ELSE
        is_consolidated := FALSE;
        consolidation_date := NULL;
        total_migrations := 0;
        schema_version := 'pre-consolidation';
        next_migration_number := 1;
    END IF;
    
    RETURN NEXT;
END;
$$;

-- Function to clean up old migration records (use with caution)
CREATE OR REPLACE FUNCTION cleanup_migration_history() RETURNS TEXT
LANGUAGE plpgsql AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete old migration records (keep consolidation marker)
    DELETE FROM migrations 
    WHERE filename != '001_consolidated_schema.sql'
        AND filename LIKE '%_consolidated_historical_migration.sql';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN 'Cleaned up ' || deleted_count || ' historical migration records. Consolidation marker preserved.';
END;
$$;