#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function checkTrucksSchema() {
    try {
        const columns = await pool.query(`
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'dump_trucks' 
            ORDER BY ordinal_position
        `);
        
        console.log('dump_trucks columns:');
        columns.rows.forEach(c => console.log(`  ${c.column_name}: ${c.data_type} (${c.is_nullable})`));
        
    } catch (error) {
        console.error('Error:', error.message);
    } finally {
        await pool.end();
    }
}

checkTrucksSchema();