-- ============================================================================
-- QR Code-based Hauling Truck Trip Management System
-- Database Schema Creation Script (Consolidated with All Migrations)
-- Version: 4.0
-- Last Updated: 2025-07-19
-- ============================================================================
--
-- This file contains the complete database schema for the Hauling QR Trip System,
-- incorporating all migrations from 001 through 063. It provides a consolidated
-- reference for the entire database structure and simplifies the setup process
-- for new environments.
--
-- Major Features Included:
-- - Core data models (users, trucks, drivers, locations)
-- - Trip tracking and workflow management
-- - Multi-location workflow support (A→B→C extensions, C→B→C cycles)
-- - Dynamic assignment adaptation system
-- - Shift management system with handovers
-- - Exception handling and approval workflows
-- - System health monitoring and automated tasks
-- - Performance optimizations and analytics views
--
-- Migration History:
-- - 001-010: Core schema and initial optimizations
-- - 011-020: Dynamic assignment adaptation and multi-location workflows
-- - 021-030: Shift management system and related fixes
-- - 031-040: Performance optimizations and analytics enhancements
-- - 041-050: System health monitoring and automated tasks
-- - 051-060: Bug fixes and schema refinements
-- - 061-063: Function signature fixes and final optimizations
--
-- ============================================================================-- =
===========================================================================
-- FILE STRUCTURE
-- ============================================================================
--
-- 1. Drop Existing Objects
--    - Drop views and materialized views
--    - Drop tables with foreign key dependencies
--    - Drop functions and procedures
--    - Drop custom types and enums
--
-- 2. Extensions
--    - Enable required PostgreSQL extensions
--
-- 3. Enums and Custom Types
--    - Create all enum types with complete set of values
--
-- 4. Core Tables
--    - Users
--    - Dump Trucks
--    - Drivers
--    - Locations
--
-- 5. Operational Tables
--    - Assignments
--    - Trip Logs
--    - Approvals
--    - Scan Logs
--
-- 6. Shift Management Tables
--    - Driver Shifts
--    - Shift Handovers
--
-- 7. System Monitoring Tables
--    - System Logs
--    - System Tasks
--    - System Health Logs
--
-- 8. Indexes
--    - Core table indexes
--    - Operational table indexes
--    - Shift management indexes
--    - System monitoring indexes
--
-- 9. Functions and Procedures
--    - Utility functions
--    - Trip management functions
--    - Analytics functions
--    - Shift management functions
--
-- 10. Triggers
--    - Updated_at triggers
--    - Business logic triggers
--
-- 11. Views and Materialized Views
--    - Operational views
--    - Analytics views
--
-- 12. Initial Data Seeding
--    - Minimal data for system initialization
--
-- 13. Migration Tracking
--    - Update migration tracking table
--
-- ============================================================================-- ===
=========================================================================
-- DROP EXISTING OBJECTS
-- ============================================================================

-- Drop existing views and materialized views
DROP MATERIALIZED VIEW IF EXISTS mv_trip_performance_summary CASCADE;
DROP VIEW IF EXISTS v_realtime_dashboard CASCADE;
DROP VIEW IF EXISTS v_active_exceptions CASCADE;
DROP VIEW IF EXISTS v_trip_performance CASCADE;
DROP VIEW IF EXISTS v_trip_summary CASCADE;
DROP VIEW IF EXISTS v_active_assignments CASCADE;
DROP VIEW IF EXISTS v_assignments_with_current_drivers CASCADE;
DROP VIEW IF EXISTS v_workflow_analytics CASCADE;
DROP VIEW IF EXISTS v_dynamic_assignment_analytics CASCADE;
DROP VIEW IF EXISTS v_driver_shift_status CASCADE;
DROP VIEW IF EXISTS v_shift_assignments CASCADE;
DROP VIEW IF EXISTS v_system_health_summary CASCADE;-
- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS scan_logs CASCADE;
DROP TABLE IF EXISTS approvals CASCADE;
DROP TABLE IF EXISTS trip_logs CASCADE;
DROP TABLE IF EXISTS automated_fix_logs CASCADE;
DROP TABLE IF EXISTS system_health_logs CASCADE;
DROP TABLE IF EXISTS system_tasks CASCADE;
DROP TABLE IF EXISTS system_logs CASCADE;
DROP TABLE IF EXISTS shift_handovers CASCADE;
DROP TABLE IF EXISTS driver_shifts CASCADE;
DROP TABLE IF EXISTS assignments CASCADE;
DROP TABLE IF EXISTS locations CASCADE;
DROP TABLE IF EXISTS drivers CASCADE;
DROP TABLE IF EXISTS dump_trucks CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS migrations CASCADE;
DROP TABLE IF EXISTS consolidated_migrations CASCADE;-- Dr
op existing functions (with proper signature handling)
DROP FUNCTION IF EXISTS get_database_performance_metrics() CASCADE;
DROP FUNCTION IF EXISTS get_advanced_exception_analytics(DATE, DATE) CASCADE;
DROP FUNCTION IF EXISTS refresh_trip_performance_summary() CASCADE;
DROP FUNCTION IF EXISTS get_exception_analytics(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS create_deviation_assignment(INTEGER, INTEGER, INTEGER, INTEGER, VARCHAR, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS update_assignment_on_trip_complete() CASCADE;
DROP FUNCTION IF EXISTS calculate_trip_durations() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER, TIMESTAMP) CASCADE;
DROP FUNCTION IF EXISTS schedule_auto_activation() CASCADE;
DROP FUNCTION IF EXISTS update_all_shift_statuses() CASCADE;
DROP FUNCTION IF EXISTS get_current_driver_for_truck(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS get_current_driver_for_truck(INTEGER, DATE) CASCADE;
DROP FUNCTION IF EXISTS create_shift_assignment(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS log_system_event(VARCHAR, TEXT, JSONB, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS get_current_active_driver(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS is_shift_active_on_date(INTEGER, DATE) CASCADE;
DROP FUNCTION IF EXISTS capture_active_driver_for_trip(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS complete_shift_manually(INTEGER, TEXT) CASCADE;-- 
Drop existing types if they exist
DROP TYPE IF EXISTS user_role CASCADE;
DROP TYPE IF EXISTS truck_status CASCADE;
DROP TYPE IF EXISTS driver_status CASCADE;
DROP TYPE IF EXISTS location_type CASCADE;
DROP TYPE IF EXISTS assignment_status CASCADE;
DROP TYPE IF EXISTS trip_status CASCADE;
DROP TYPE IF EXISTS approval_status CASCADE;
DROP TYPE IF EXISTS scan_type CASCADE;
DROP TYPE IF EXISTS shift_type CASCADE;
DROP TYPE IF EXISTS shift_status CASCADE;-- 
============================================================================
-- EXTENSIONS
-- ============================================================================

-- Enable pg_trgm extension for text search optimization
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Enable btree_gin extension for GIN indexes on non-jsonb columns
CREATE EXTENSION IF NOT EXISTS btree_gin;-- =====
=======================================================================
-- ENUMS AND CUSTOM TYPES
-- ============================================================================

-- User roles
CREATE TYPE user_role AS ENUM ('admin', 'supervisor', 'operator');

-- Truck status
CREATE TYPE truck_status AS ENUM ('active', 'inactive', 'maintenance', 'retired');

-- Driver status
CREATE TYPE driver_status AS ENUM ('active', 'inactive', 'on_leave', 'terminated');

-- Location types
CREATE TYPE location_type AS ENUM ('loading', 'unloading', 'checkpoint');

-- Assignment status
CREATE TYPE assignment_status AS ENUM ('pending_approval', 'assigned', 'in_progress', 'completed', 'cancelled');

-- Trip status (A→B→A or A→B→C with approval)
CREATE TYPE trip_status AS ENUM (
    'assigned',
    'loading_start',
    'loading_end', 
    'unloading_start',
    'unloading_end',
    'trip_completed',
    'exception_pending',
    'exception_triggered',
    'cancelled'
);

-- Approval status for exceptions
CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected');

-- Scan types for QR code scanning
CREATE TYPE scan_type AS ENUM ('location_scan', 'truck_scan', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end');

-- Shift types
CREATE TYPE shift_type AS ENUM ('day', 'night', 'morning', 'evening', 'custom');

-- Shift status
CREATE TYPE shift_status AS ENUM ('scheduled', 'active', 'completed', 'cancelled');--
 Add comments to document enum types
COMMENT ON TYPE user_role IS 'User permission levels (admin, supervisor, operator)';
COMMENT ON TYPE truck_status IS 'Current operational status of dump trucks';
COMMENT ON TYPE driver_status IS 'Current employment status of drivers';
COMMENT ON TYPE location_type IS 'Type of location in the hauling workflow';
COMMENT ON TYPE assignment_status IS 'Current status of driver-truck assignments';
COMMENT ON TYPE trip_status IS 'Current stage in the trip workflow cycle';
COMMENT ON TYPE approval_status IS 'Status of exception approval requests';
COMMENT ON TYPE scan_type IS 'Type of QR code scan event';
COMMENT ON TYPE shift_type IS 'Type of driver shift (day, night, etc.)';
COMMENT ON TYPE shift_status IS 'Current status of a scheduled shift';-- =======
=====================================================================
-- CORE TABLES
-- ============================================================================

-- ============================================================================
-- TABLE: users
-- Purpose: Admin and operator authentication
-- ============================================================================

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role user_role NOT NULL DEFAULT 'operator',
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    last_login TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE users IS 'System users with authentication and authorization information';
COMMENT ON COLUMN users.id IS 'Unique identifier for the user';
COMMENT ON COLUMN users.username IS 'Unique login username';
COMMENT ON COLUMN users.email IS 'User email address for notifications';
COMMENT ON COLUMN users.password_hash IS 'Bcrypt hashed password';
COMMENT ON COLUMN users.full_name IS 'User full name for display';
COMMENT ON COLUMN users.role IS 'User permission level (admin, supervisor, operator)';
COMMENT ON COLUMN users.status IS 'Account status (active or inactive)';
COMMENT ON COLUMN users.last_login IS 'Timestamp of last successful login';
COMMENT ON COLUMN users.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN users.updated_at IS 'Timestamp when record was last updated';--
 ============================================================================
-- TABLE: dump_trucks
-- Purpose: Truck information with QR codes
-- ============================================================================

CREATE TABLE dump_trucks (
    id SERIAL PRIMARY KEY,
    truck_number VARCHAR(20) UNIQUE NOT NULL,
    license_plate VARCHAR(20) UNIQUE NOT NULL,
    make VARCHAR(50),
    model VARCHAR(50),
    year INTEGER,
    capacity_tons DECIMAL(5,2),
    qr_code_data JSONB NOT NULL, -- Converted to JSONB for performance (Migration 012)
    status truck_status NOT NULL DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE dump_trucks IS 'Dump trucks used for hauling operations';
COMMENT ON COLUMN dump_trucks.id IS 'Unique identifier for the truck';
COMMENT ON COLUMN dump_trucks.truck_number IS 'Company-assigned truck number';
COMMENT ON COLUMN dump_trucks.license_plate IS 'Government-issued license plate';
COMMENT ON COLUMN dump_trucks.make IS 'Truck manufacturer';
COMMENT ON COLUMN dump_trucks.model IS 'Truck model';
COMMENT ON COLUMN dump_trucks.year IS 'Manufacturing year';
COMMENT ON COLUMN dump_trucks.capacity_tons IS 'Maximum load capacity in tons';
COMMENT ON COLUMN dump_trucks.qr_code_data IS 'JSON data for QR code generation and validation';
COMMENT ON COLUMN dump_trucks.status IS 'Current operational status';
COMMENT ON COLUMN dump_trucks.notes IS 'Additional notes about the truck';
COMMENT ON COLUMN dump_trucks.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN dump_trucks.updated_at IS 'Timestamp when record was last updated';-- ==
==========================================================================
-- TABLE: drivers
-- Purpose: Driver information and details
-- ============================================================================

CREATE TABLE drivers (
    id SERIAL PRIMARY KEY,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    license_number VARCHAR(30) UNIQUE NOT NULL,
    license_expiry DATE NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    hire_date DATE NOT NULL,
    status driver_status NOT NULL DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE drivers IS 'Truck drivers with licensing and contact information';
COMMENT ON COLUMN drivers.id IS 'Unique identifier for the driver';
COMMENT ON COLUMN drivers.employee_id IS 'Company-assigned employee ID';
COMMENT ON COLUMN drivers.full_name IS 'Driver full name';
COMMENT ON COLUMN drivers.license_number IS 'Government-issued driver license number';
COMMENT ON COLUMN drivers.license_expiry IS 'Date when driver license expires';
COMMENT ON COLUMN drivers.phone IS 'Contact phone number';
COMMENT ON COLUMN drivers.email IS 'Contact email address';
COMMENT ON COLUMN drivers.address IS 'Residential address';
COMMENT ON COLUMN drivers.hire_date IS 'Date when driver was hired';
COMMENT ON COLUMN drivers.status IS 'Current employment status';
COMMENT ON COLUMN drivers.notes IS 'Additional notes about the driver';
COMMENT ON COLUMN drivers.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN drivers.updated_at IS 'Timestamp when record was last updated';-- =
===========================================================================
-- TABLE: locations
-- Purpose: Loading/unloading points with QR codes
-- ============================================================================

CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    location_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    type location_type NOT NULL,
    address TEXT,
    coordinates VARCHAR(50), -- "latitude,longitude"
    qr_code_data JSONB NOT NULL, -- Converted to JSONB for performance (Migration 012)
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- Changed from is_active to status for consistency
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE locations IS 'Loading, unloading, and checkpoint locations with QR codes';
COMMENT ON COLUMN locations.id IS 'Unique identifier for the location';
COMMENT ON COLUMN locations.location_code IS 'Short code for location identification';
COMMENT ON COLUMN locations.name IS 'Descriptive name of the location';
COMMENT ON COLUMN locations.type IS 'Type of location (loading, unloading, checkpoint)';
COMMENT ON COLUMN locations.address IS 'Physical address of the location';
COMMENT ON COLUMN locations.coordinates IS 'GPS coordinates in format "latitude,longitude"';
COMMENT ON COLUMN locations.qr_code_data IS 'JSON data for QR code generation and validation';
COMMENT ON COLUMN locations.status IS 'Current status of the location (active or inactive)';
COMMENT ON COLUMN locations.notes IS 'Additional notes about the location';
COMMENT ON COLUMN locations.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN locations.updated_at IS 'Timestamp when record was last updated';-- ==
==========================================================================
-- OPERATIONAL TABLES
-- ============================================================================

-- ============================================================================
-- TABLE: assignments
-- Purpose: Assign truck + driver + route (flexible routes)
-- ============================================================================

CREATE TABLE assignments (
    id SERIAL PRIMARY KEY,
    assignment_code VARCHAR(50) UNIQUE,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER NOT NULL REFERENCES drivers(id) ON DELETE CASCADE,
    loading_location_id INTEGER NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
    unloading_location_id INTEGER NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
    status assignment_status NOT NULL DEFAULT 'assigned',
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    assigned_date DATE, -- Made optional (Migration 013)
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    expected_loads_per_day INTEGER DEFAULT 1,
    
    -- Dynamic Assignment Adaptation Fields (Migration 015)
    is_adaptive BOOLEAN DEFAULT false, -- Indicates if assignment was created by dynamic adapter
    adaptation_strategy VARCHAR(50), -- pattern_based, proximity_based, efficiency_based, manual_override
    adaptation_confidence VARCHAR(20), -- high, medium, low
    adaptation_metadata JSONB, -- Additional adaptation data and analytics
    auto_created BOOLEAN DEFAULT false, -- Indicates if assignment was auto-created by system
    
    -- Shift Management Integration (Migration 017)
    shift_id INTEGER, -- Will be set as foreign key after shift_id table is created
    is_shift_assignment BOOLEAN DEFAULT false,
    shift_handover_id INTEGER, -- Will be set as foreign key after shift_handovers table is created
    
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE assignments IS 'Driver and truck assignments to specific routes';
COMMENT ON COLUMN assignments.id IS 'Unique identifier for the assignment';
COMMENT ON COLUMN assignments.assignment_code IS 'Unique code for assignment identification';
COMMENT ON COLUMN assignments.truck_id IS 'Reference to assigned dump truck';
COMMENT ON COLUMN assignments.driver_id IS 'Reference to assigned driver';
COMMENT ON COLUMN assignments.loading_location_id IS 'Reference to loading location';
COMMENT ON COLUMN assignments.unloading_location_id IS 'Reference to unloading location';
COMMENT ON COLUMN assignments.status IS 'Current status of the assignment';
COMMENT ON COLUMN assignments.priority IS 'Assignment priority level';
COMMENT ON COLUMN assignments.assigned_date IS 'Date when assignment is scheduled';
COMMENT ON COLUMN assignments.start_time IS 'Timestamp when assignment started';
COMMENT ON COLUMN assignments.end_time IS 'Timestamp when assignment completed';
COMMENT ON COLUMN assignments.expected_loads_per_day IS 'Target number of trips for this assignment';
COMMENT ON COLUMN assignments.is_adaptive IS 'Whether assignment was created by dynamic adaptation system';
COMMENT ON COLUMN assignments.adaptation_strategy IS 'Strategy used for dynamic assignment creation';
COMMENT ON COLUMN assignments.adaptation_confidence IS 'Confidence level of the adaptation decision';
COMMENT ON COLUMN assignments.adaptation_metadata IS 'Additional data about the adaptation process';
COMMENT ON COLUMN assignments.auto_created IS 'Whether assignment was automatically created';
COMMENT ON COLUMN assignments.shift_id IS 'Reference to associated driver shift';
COMMENT ON COLUMN assignments.is_shift_assignment IS 'Whether assignment is tied to a specific shift';
COMMENT ON COLUMN assignments.shift_handover_id IS 'Reference to shift handover if applicable';
COMMENT ON COLUMN assignments.notes IS 'Additional notes about the assignment';
COMMENT ON COLUMN assignments.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN assignments.updated_at IS 'Timestamp when record was last updated';-- =
===========================================================================
-- TABLE: trip_logs
-- Purpose: Complete trip tracking with timestamps (A→B→A or A→B→C)
-- CRITICAL: Trip deduplication logic prevents multiple trips per truck journey
-- ============================================================================

CREATE TABLE trip_logs (
    id SERIAL PRIMARY KEY,
    assignment_id INTEGER NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
    trip_number INTEGER NOT NULL, -- Sequential per assignment
    status trip_status NOT NULL DEFAULT 'assigned',

    -- Trip timestamps for complete A→B→A cycle
    loading_start_time TIMESTAMP,
    loading_end_time TIMESTAMP,
    unloading_start_time TIMESTAMP,
    unloading_end_time TIMESTAMP,
    trip_completed_time TIMESTAMP,

    -- Actual locations (may differ from assignment if exception)
    actual_loading_location_id INTEGER REFERENCES locations(id),
    actual_unloading_location_id INTEGER REFERENCES locations(id),

    -- Exception handling
    is_exception BOOLEAN NOT NULL DEFAULT false,
    exception_reason TEXT,
    exception_approved_by INTEGER REFERENCES users(id),
    exception_approved_at TIMESTAMP,

    -- Performance metrics (auto-calculated by trigger)
    total_duration_minutes INTEGER, -- Calculated field
    loading_duration_minutes INTEGER, -- Calculated field
    travel_duration_minutes INTEGER, -- Calculated field
    unloading_duration_minutes INTEGER, -- Calculated field
    
    -- Multi-Location Workflow Fields (Migration 016)
    location_sequence JSONB, -- Complete sequence of locations for extended trips
    is_extended_trip BOOLEAN DEFAULT FALSE, -- Indicates A→B→C instead of A→B→A
    workflow_type VARCHAR(50) DEFAULT 'standard' CHECK (workflow_type IN ('standard', 'extended', 'cycle', 'dynamic')),
    baseline_trip_id INTEGER REFERENCES trip_logs(id) ON DELETE SET NULL, -- For tracking trip extensions
    cycle_number INTEGER DEFAULT 1 CHECK (cycle_number >= 1), -- For tracking C→B→C cycles
    
    -- Shift Management Integration (Migration 017)
    performed_by_driver_id INTEGER REFERENCES drivers(id), -- Actual driver who performed the trip
    
    notes JSONB, -- Converted to JSONB for performance (Migration 012)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Ensure unique trip numbers per assignment
    UNIQUE(assignment_id, trip_number),

    -- Data integrity constraints (Migration 012)
    CONSTRAINT chk_trip_timing_sequence CHECK (
        (loading_start_time IS NULL OR loading_end_time IS NULL OR loading_end_time >= loading_start_time) AND
        (loading_end_time IS NULL OR unloading_start_time IS NULL OR unloading_start_time >= loading_end_time) AND
        (unloading_start_time IS NULL OR unloading_end_time IS NULL OR unloading_end_time >= unloading_start_time) AND
        (unloading_end_time IS NULL OR trip_completed_time IS NULL OR trip_completed_time >= unloading_end_time)
    ),
    CONSTRAINT chk_duration_non_negative CHECK (
        (total_duration_minutes IS NULL OR total_duration_minutes >= 0) AND
        (loading_duration_minutes IS NULL OR loading_duration_minutes >= 0) AND
        (travel_duration_minutes IS NULL OR travel_duration_minutes >= 0) AND
        (unloading_duration_minutes IS NULL OR unloading_duration_minutes >= 0)
    )
);

-- Add table and column comments
COMMENT ON TABLE trip_logs IS 'Individual trip records with complete workflow tracking';
COMMENT ON COLUMN trip_logs.id IS 'Unique identifier for the trip';
COMMENT ON COLUMN trip_logs.assignment_id IS 'Reference to associated assignment';
COMMENT ON COLUMN trip_logs.trip_number IS 'Sequential trip number within the assignment';
COMMENT ON COLUMN trip_logs.status IS 'Current status in the trip workflow';
COMMENT ON COLUMN trip_logs.loading_start_time IS 'Timestamp when loading began';
COMMENT ON COLUMN trip_logs.loading_end_time IS 'Timestamp when loading completed';
COMMENT ON COLUMN trip_logs.unloading_start_time IS 'Timestamp when unloading began';
COMMENT ON COLUMN trip_logs.unloading_end_time IS 'Timestamp when unloading completed';
COMMENT ON COLUMN trip_logs.trip_completed_time IS 'Timestamp when entire trip was completed';
COMMENT ON COLUMN trip_logs.actual_loading_location_id IS 'Reference to actual loading location used';
COMMENT ON COLUMN trip_logs.actual_unloading_location_id IS 'Reference to actual unloading location used';
COMMENT ON COLUMN trip_logs.is_exception IS 'Whether trip had any exceptions from standard workflow';
COMMENT ON COLUMN trip_logs.exception_reason IS 'Description of the exception reason';
COMMENT ON COLUMN trip_logs.exception_approved_by IS 'Reference to user who approved the exception';
COMMENT ON COLUMN trip_logs.exception_approved_at IS 'Timestamp when exception was approved';
COMMENT ON COLUMN trip_logs.total_duration_minutes IS 'Total trip duration in minutes';
COMMENT ON COLUMN trip_logs.loading_duration_minutes IS 'Loading phase duration in minutes';
COMMENT ON COLUMN trip_logs.travel_duration_minutes IS 'Travel phase duration in minutes';
COMMENT ON COLUMN trip_logs.unloading_duration_minutes IS 'Unloading phase duration in minutes';
COMMENT ON COLUMN trip_logs.location_sequence IS 'Complete sequence of locations for multi-location trips';
COMMENT ON COLUMN trip_logs.is_extended_trip IS 'Whether trip follows extended A→B→C pattern';
COMMENT ON COLUMN trip_logs.workflow_type IS 'Type of workflow pattern for this trip';
COMMENT ON COLUMN trip_logs.baseline_trip_id IS 'Reference to original trip for extensions';
COMMENT ON COLUMN trip_logs.cycle_number IS 'Cycle number for repeated C→B→C patterns';
COMMENT ON COLUMN trip_logs.performed_by_driver_id IS 'Reference to actual driver who performed the trip';
COMMENT ON COLUMN trip_logs.notes IS 'Additional notes and metadata about the trip';
COMMENT ON COLUMN trip_logs.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN trip_logs.updated_at IS 'Timestamp when record was last updated';-- =======
=====================================================================
-- TABLE: approvals
-- Purpose: Exception handling workflow (A→B→C instead of A→B→A)
-- ============================================================================

CREATE TABLE approvals (
    id SERIAL PRIMARY KEY,
    trip_log_id INTEGER NOT NULL REFERENCES trip_logs(id) ON DELETE CASCADE,
    exception_type VARCHAR(50) NOT NULL,
    exception_description TEXT NOT NULL,
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    reported_by INTEGER REFERENCES users(id),
    requested_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INTEGER REFERENCES users(id),
    reviewed_at TIMESTAMP,
    status approval_status NOT NULL DEFAULT 'pending',
    notes TEXT,

    -- Dynamic Assignment Adaptation Integration Fields (Migration 015)
    is_adaptive_exception BOOLEAN DEFAULT false, -- Indicates if exception was created by dynamic adapter
    adaptation_strategy VARCHAR(50), -- pattern_based, proximity_based, efficiency_based, manual_override
    adaptation_confidence VARCHAR(20), -- high, medium, low
    auto_approved BOOLEAN DEFAULT false, -- Indicates if exception was auto-approved by dynamic adapter
    adaptation_metadata JSONB, -- Additional adaptation data and decision factors
    suggested_assignment_id INTEGER REFERENCES assignments(id), -- Reference to suggested/created assignment

    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE approvals IS 'Exception approval workflow for non-standard trips';
COMMENT ON COLUMN approvals.id IS 'Unique identifier for the approval request';
COMMENT ON COLUMN approvals.trip_log_id IS 'Reference to associated trip log';
COMMENT ON COLUMN approvals.exception_type IS 'Type of exception (route_deviation, schedule_change, etc.)';
COMMENT ON COLUMN approvals.exception_description IS 'Detailed description of the exception';
COMMENT ON COLUMN approvals.severity IS 'Severity level of the exception';
COMMENT ON COLUMN approvals.reported_by IS 'Reference to user who reported the exception';
COMMENT ON COLUMN approvals.requested_at IS 'Timestamp when approval was requested';
COMMENT ON COLUMN approvals.reviewed_by IS 'Reference to user who reviewed the exception';
COMMENT ON COLUMN approvals.reviewed_at IS 'Timestamp when exception was reviewed';
COMMENT ON COLUMN approvals.status IS 'Current status of the approval request';
COMMENT ON COLUMN approvals.notes IS 'Additional notes about the exception';
COMMENT ON COLUMN approvals.is_adaptive_exception IS 'Whether exception was created by dynamic adaptation system';
COMMENT ON COLUMN approvals.adaptation_strategy IS 'Strategy used for exception handling';
COMMENT ON COLUMN approvals.adaptation_confidence IS 'Confidence level of the adaptation decision';
COMMENT ON COLUMN approvals.auto_approved IS 'Whether exception was automatically approved';
COMMENT ON COLUMN approvals.adaptation_metadata IS 'Additional data about the adaptation process';
COMMENT ON COLUMN approvals.suggested_assignment_id IS 'Reference to suggested new assignment';
COMMENT ON COLUMN approvals.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN approvals.updated_at IS 'Timestamp when record was last updated';--
 ============================================================================
-- TABLE: scan_logs
-- Purpose: Audit trail for all QR code scans
-- ============================================================================

CREATE TABLE scan_logs (
    id SERIAL PRIMARY KEY,
    trip_log_id INTEGER REFERENCES trip_logs(id) ON DELETE SET NULL,
    scan_type scan_type NOT NULL,
    scanned_data TEXT NOT NULL, -- Raw QR code JSON data
    scanned_location_id INTEGER REFERENCES locations(id) ON DELETE SET NULL, -- Migration 004/005
    scanned_truck_id INTEGER REFERENCES dump_trucks(id),
    scanner_user_id INTEGER REFERENCES users(id),
    scan_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_valid BOOLEAN NOT NULL DEFAULT true,
    validation_error TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE scan_logs IS 'Audit trail for all QR code scanning events';
COMMENT ON COLUMN scan_logs.id IS 'Unique identifier for the scan event';
COMMENT ON COLUMN scan_logs.trip_log_id IS 'Reference to associated trip log if applicable';
COMMENT ON COLUMN scan_logs.scan_type IS 'Type of scan event';
COMMENT ON COLUMN scan_logs.scanned_data IS 'Raw data captured from QR code';
COMMENT ON COLUMN scan_logs.scanned_location_id IS 'Reference to scanned location if applicable';
COMMENT ON COLUMN scan_logs.scanned_truck_id IS 'Reference to scanned truck if applicable';
COMMENT ON COLUMN scan_logs.scanner_user_id IS 'Reference to user who performed the scan';
COMMENT ON COLUMN scan_logs.scan_timestamp IS 'Timestamp when scan occurred';
COMMENT ON COLUMN scan_logs.is_valid IS 'Whether scan data was valid';
COMMENT ON COLUMN scan_logs.validation_error IS 'Error message if validation failed';
COMMENT ON COLUMN scan_logs.ip_address IS 'IP address of scanning device';
COMMENT ON COLUMN scan_logs.user_agent IS 'User agent of scanning device';
COMMENT ON COLUMN scan_logs.created_at IS 'Timestamp when record was created';-- =
===========================================================================
-- SHIFT MANAGEMENT TABLES
-- ============================================================================

-- ============================================================================
-- TABLE: driver_shifts
-- Purpose: Track driver shift schedules and assignments
-- ============================================================================

CREATE TABLE driver_shifts (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER NOT NULL REFERENCES drivers(id) ON DELETE CASCADE,
    shift_type shift_type NOT NULL DEFAULT 'day',
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status shift_status NOT NULL DEFAULT 'scheduled',
    
    -- Handover tracking
    previous_shift_id INTEGER REFERENCES driver_shifts(id),
    handover_notes TEXT,
    handover_completed_at TIMESTAMP,
    
    -- Assignment integration
    assignment_id INTEGER, -- Will be set as foreign key after circular reference is resolved
    auto_created BOOLEAN DEFAULT false,
    
    -- Recurrence pattern for shift scheduling
    recurrence_pattern VARCHAR(50), -- daily, weekly, weekdays, custom
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(truck_id, shift_date, start_time)
);

-- Add table and column comments
COMMENT ON TABLE driver_shifts IS 'Driver shift schedules and assignments';
COMMENT ON COLUMN driver_shifts.id IS 'Unique identifier for the shift';
COMMENT ON COLUMN driver_shifts.truck_id IS 'Reference to assigned dump truck';
COMMENT ON COLUMN driver_shifts.driver_id IS 'Reference to assigned driver';
COMMENT ON COLUMN driver_shifts.shift_type IS 'Type of shift (day, night, etc.)';
COMMENT ON COLUMN driver_shifts.shift_date IS 'Date when shift is scheduled';
COMMENT ON COLUMN driver_shifts.start_time IS 'Time when shift starts';
COMMENT ON COLUMN driver_shifts.end_time IS 'Time when shift ends';
COMMENT ON COLUMN driver_shifts.status IS 'Current status of the shift';
COMMENT ON COLUMN driver_shifts.previous_shift_id IS 'Reference to previous shift for handover';
COMMENT ON COLUMN driver_shifts.handover_notes IS 'Notes from shift handover';
COMMENT ON COLUMN driver_shifts.handover_completed_at IS 'Timestamp when handover was completed';
COMMENT ON COLUMN driver_shifts.assignment_id IS 'Reference to associated assignment';
COMMENT ON COLUMN driver_shifts.auto_created IS 'Whether shift was automatically created';
COMMENT ON COLUMN driver_shifts.recurrence_pattern IS 'Pattern for recurring shift scheduling';
COMMENT ON COLUMN driver_shifts.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN driver_shifts.updated_at IS 'Timestamp when record was last updated';-- =======
=====================================================================
-- TABLE: shift_handovers
-- Purpose: Track handovers between driver shifts
-- ============================================================================

CREATE TABLE shift_handovers (
    id SERIAL PRIMARY KEY,
    from_shift_id INTEGER NOT NULL REFERENCES driver_shifts(id) ON DELETE CASCADE,
    to_shift_id INTEGER NOT NULL REFERENCES driver_shifts(id) ON DELETE CASCADE,
    handover_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    truck_condition TEXT,
    fuel_level VARCHAR(20),
    issues_reported TEXT,
    handover_notes TEXT,
    completed_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(from_shift_id, to_shift_id)
);

-- Add table and column comments
COMMENT ON TABLE shift_handovers IS 'Handover records between driver shifts';
COMMENT ON COLUMN shift_handovers.id IS 'Unique identifier for the handover';
COMMENT ON COLUMN shift_handovers.from_shift_id IS 'Reference to outgoing shift';
COMMENT ON COLUMN shift_handovers.to_shift_id IS 'Reference to incoming shift';
COMMENT ON COLUMN shift_handovers.handover_time IS 'Timestamp when handover occurred';
COMMENT ON COLUMN shift_handovers.truck_condition IS 'Condition of truck at handover';
COMMENT ON COLUMN shift_handovers.fuel_level IS 'Fuel level at handover';
COMMENT ON COLUMN shift_handovers.issues_reported IS 'Issues reported during handover';
COMMENT ON COLUMN shift_handovers.handover_notes IS 'Additional notes about the handover';
COMMENT ON COLUMN shift_handovers.completed_by IS 'Reference to user who completed the handover';
COMMENT ON COLUMN shift_handovers.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN shift_handovers.updated_at IS 'Timestamp when record was last updated';-- ==
==========================================================================
-- SYSTEM MONITORING TABLES
-- ============================================================================

-- ============================================================================
-- TABLE: system_logs
-- Purpose: Track system events and errors
-- ============================================================================

CREATE TABLE system_logs (
    id SERIAL PRIMARY KEY,
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add table and column comments
COMMENT ON TABLE system_logs IS 'System event and error logs';
COMMENT ON COLUMN system_logs.id IS 'Unique identifier for the log entry';
COMMENT ON COLUMN system_logs.log_type IS 'Type of log entry (info, warning, error, etc.)';
COMMENT ON COLUMN system_logs.message IS 'Log message';
COMMENT ON COLUMN system_logs.details IS 'Additional details in JSON format';
COMMENT ON COLUMN system_logs.user_id IS 'Reference to user associated with the event';
COMMENT ON COLUMN system_logs.created_at IS 'Timestamp when log entry was created';-- ==
==========================================================================
-- TABLE: system_tasks
-- Purpose: Track scheduled system maintenance tasks
-- ============================================================================

CREATE TABLE system_tasks (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER,
    auto_executable BOOLEAN DEFAULT false,
    metadata JSONB,
    created_by INTEGER REFERENCES users(id)
);

-- Add table and column comments
COMMENT ON TABLE system_tasks IS 'Scheduled system maintenance tasks';
COMMENT ON COLUMN system_tasks.id IS 'Unique identifier for the task';
COMMENT ON COLUMN system_tasks.type IS 'Type of task (maintenance, backup, cleanup, etc.)';
COMMENT ON COLUMN system_tasks.priority IS 'Priority level of the task';
COMMENT ON COLUMN system_tasks.status IS 'Current status of the task';
COMMENT ON COLUMN system_tasks.title IS 'Short title describing the task';
COMMENT ON COLUMN system_tasks.description IS 'Detailed description of the task';
COMMENT ON COLUMN system_tasks.created_at IS 'Timestamp when task was created';
COMMENT ON COLUMN system_tasks.scheduled_for IS 'Timestamp when task is scheduled to run';
COMMENT ON COLUMN system_tasks.completed_at IS 'Timestamp when task was completed';
COMMENT ON COLUMN system_tasks.estimated_duration IS 'Estimated duration in minutes';
COMMENT ON COLUMN system_tasks.auto_executable IS 'Whether task can be executed automatically';
COMMENT ON COLUMN system_tasks.metadata IS 'Additional task metadata in JSON format';
COMMENT ON COLUMN system_tasks.created_by IS 'Reference to user who created the task';-- =======
=====================================================================
-- TABLE: system_health_logs
-- Purpose: Track system health metrics
-- ============================================================================

CREATE TABLE system_health_logs (
    id SERIAL PRIMARY KEY,
    check_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('healthy', 'warning', 'critical', 'unknown')),
    metric_name VARCHAR(100) NOT NULL,
    metric_value NUMERIC,
    threshold_warning NUMERIC,
    threshold_critical NUMERIC,
    details JSONB,
    check_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolution_timestamp TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    auto_resolved BOOLEAN DEFAULT false
);

-- Add table and column comments
COMMENT ON TABLE system_health_logs IS 'System health monitoring metrics';
COMMENT ON COLUMN system_health_logs.id IS 'Unique identifier for the health log entry';
COMMENT ON COLUMN system_health_logs.check_type IS 'Type of health check (database, api, memory, etc.)';
COMMENT ON COLUMN system_health_logs.status IS 'Status of the health check';
COMMENT ON COLUMN system_health_logs.metric_name IS 'Name of the metric being checked';
COMMENT ON COLUMN system_health_logs.metric_value IS 'Value of the metric';
COMMENT ON COLUMN system_health_logs.threshold_warning IS 'Warning threshold for the metric';
COMMENT ON COLUMN system_health_logs.threshold_critical IS 'Critical threshold for the metric';
COMMENT ON COLUMN system_health_logs.details IS 'Additional details in JSON format';
COMMENT ON COLUMN system_health_logs.check_timestamp IS 'Timestamp when check was performed';
COMMENT ON COLUMN system_health_logs.resolution_timestamp IS 'Timestamp when issue was resolved';
COMMENT ON COLUMN system_health_logs.resolution_notes IS 'Notes about the resolution';
COMMENT ON COLUMN system_health_logs.auto_resolved IS 'Whether issue was automatically resolved';-- ====
========================================================================
-- TABLE: automated_fix_logs
-- Purpose: Track automated fixes applied by the system
-- ============================================================================

CREATE TABLE automated_fix_logs (
    id SERIAL PRIMARY KEY,
    fix_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INTEGER NOT NULL,
    issue_description TEXT NOT NULL,
    fix_description TEXT NOT NULL,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    applied_by VARCHAR(100) DEFAULT 'system',
    success BOOLEAN NOT NULL,
    error_message TEXT,
    details JSONB
);

-- Add table and column comments
COMMENT ON TABLE automated_fix_logs IS 'Log of automated fixes applied by the system';
COMMENT ON COLUMN automated_fix_logs.id IS 'Unique identifier for the fix log entry';
COMMENT ON COLUMN automated_fix_logs.fix_type IS 'Type of fix applied';
COMMENT ON COLUMN automated_fix_logs.entity_type IS 'Type of entity that was fixed';
COMMENT ON COLUMN automated_fix_logs.entity_id IS 'ID of the entity that was fixed';
COMMENT ON COLUMN automated_fix_logs.issue_description IS 'Description of the issue that was fixed';
COMMENT ON COLUMN automated_fix_logs.fix_description IS 'Description of the fix that was applied';
COMMENT ON COLUMN automated_fix_logs.applied_at IS 'Timestamp when fix was applied';
COMMENT ON COLUMN automated_fix_logs.applied_by IS 'Name of system component that applied the fix';
COMMENT ON COLUMN automated_fix_logs.success IS 'Whether the fix was successful';
COMMENT ON COLUMN automated_fix_logs.error_message IS 'Error message if fix failed';
COMMENT ON COLUMN automated_fix_logs.details IS 'Additional details in JSON format';-
- ============================================================================
-- INDEXES
-- ============================================================================

-- ============================================================================
-- Core Table Indexes
-- ============================================================================

-- Users
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- Dump trucks
CREATE INDEX idx_trucks_number ON dump_trucks(truck_number);
CREATE INDEX idx_trucks_status ON dump_trucks(status);
CREATE INDEX idx_trucks_license ON dump_trucks(license_plate);
CREATE INDEX idx_trucks_active_status ON dump_trucks(status, truck_number) WHERE status = 'active'; -- Migration 012
CREATE INDEX idx_trucks_qr_data_gin ON dump_trucks USING gin(qr_code_data) WHERE qr_code_data IS NOT NULL; -- Migration 012

-- Drivers
CREATE INDEX idx_drivers_employee_id ON drivers(employee_id);
CREATE INDEX idx_drivers_status ON drivers(status);
CREATE INDEX idx_drivers_license ON drivers(license_number);
CREATE INDEX idx_drivers_active_status ON drivers(status, employee_id) WHERE status = 'active'; -- Migration 012

-- Locations
CREATE INDEX idx_locations_code ON locations(location_code);
CREATE INDEX idx_locations_type ON locations(type);
CREATE INDEX idx_locations_active ON locations(status) WHERE status = 'active'; -- Updated for status column
CREATE INDEX idx_locations_active_type ON locations(type, location_code) WHERE status = 'active'; -- Migration 012
CREATE INDEX idx_locations_qr_data_gin ON locations USING gin(qr_code_data) WHERE qr_code_data IS NOT NULL; -- Migration 012-- ======
======================================================================
-- Operational Table Indexes
-- ============================================================================

-- Assignments (with all migration updates)
CREATE INDEX idx_assignments_truck ON assignments(truck_id);
CREATE INDEX idx_assignments_driver ON assignments(driver_id);
CREATE INDEX idx_assignments_date ON assignments(assigned_date);
CREATE INDEX idx_assignments_status ON assignments(status);
CREATE INDEX idx_assignments_assignment_code ON assignments(assignment_code); -- Migration 001
CREATE INDEX idx_assignments_priority ON assignments(priority); -- Migration 001
CREATE INDEX idx_assignments_truck_driver_date ON assignments(truck_id, driver_id, assigned_date); -- Migration 008
CREATE INDEX idx_assignments_locations_date ON assignments(loading_location_id, assigned_date, status); -- Migration 008
CREATE INDEX idx_assignments_truck_locations ON assignments(truck_id, loading_location_id, unloading_location_id, assigned_date); -- Migration 012
CREATE INDEX idx_assignments_status_date ON assignments(status, assigned_date) WHERE status IN ('assigned', 'in_progress'); -- Migration 012

-- Dynamic Assignment Adaptation Indexes
CREATE INDEX idx_assignments_adaptive ON assignments(is_adaptive) WHERE is_adaptive = true;
CREATE INDEX idx_assignments_adaptation_strategy ON assignments(adaptation_strategy) WHERE adaptation_strategy IS NOT NULL;
CREATE INDEX idx_assignments_adaptation_confidence ON assignments(adaptation_confidence) WHERE adaptation_confidence IS NOT NULL;
CREATE INDEX idx_assignments_adaptive_status ON assignments(is_adaptive, status, created_at) WHERE is_adaptive = true;
CREATE INDEX idx_assignments_adaptation_metadata_gin ON assignments USING gin(adaptation_metadata) WHERE adaptation_metadata IS NOT NULL;

-- Assignment constraints (updated from migrations)
CREATE UNIQUE INDEX idx_assignments_exact_duplicate
ON assignments (truck_id, loading_location_id, unloading_location_id)
WHERE status IN ('assigned', 'in_progress'); -- Migration 012 (removed assigned_date)

-- Trip logs (with all performance optimizations)
CREATE INDEX idx_trips_assignment ON trip_logs(assignment_id);
CREATE INDEX idx_trips_status ON trip_logs(status);
CREATE INDEX idx_trips_date ON trip_logs(created_at);
CREATE INDEX idx_trips_exception ON trip_logs(is_exception);
CREATE INDEX idx_trips_assignment_status_date ON trip_logs(assignment_id, status, created_at); -- Migration 008
CREATE INDEX idx_trips_actual_locations ON trip_logs(actual_loading_location_id, actual_unloading_location_id); -- Migration 008
CREATE INDEX idx_trips_exception_status ON trip_logs(is_exception, status) WHERE is_exception = true; -- Migration 008
CREATE INDEX idx_trips_assignment_status_exception ON trip_logs(assignment_id, status, is_exception, created_at); -- Migration 012
CREATE INDEX idx_trips_duration_metrics ON trip_logs(total_duration_minutes, loading_duration_minutes) WHERE total_duration_minutes IS NOT NULL; -- Migration 012
CREATE INDEX idx_trip_logs_notes_gin ON trip_logs USING gin(notes) WHERE notes IS NOT NULL; -- Migration 012
CREATE INDEX idx_trips_performed_by_driver ON trip_logs(performed_by_driver_id);
CREATE INDEX idx_trips_workflow_type ON trip_logs(workflow_type);
CREATE INDEX idx_trips_location_sequence_gin ON trip_logs USING gin(location_sequence) WHERE location_sequence IS NOT NULL;

-- Approvals (with all migration updates)
CREATE INDEX idx_approvals_trip ON approvals(trip_log_id);
CREATE INDEX idx_approvals_status ON approvals(status);
CREATE INDEX idx_approvals_requested_at ON approvals(requested_at);
CREATE INDEX idx_approvals_severity ON approvals(severity); -- Migration 002/003
CREATE INDEX idx_approvals_reported_by ON approvals(reported_by); -- Migration 002/003/008
CREATE INDEX idx_approvals_trip_status_created ON approvals(trip_log_id, status, created_at); -- Migration 008
CREATE INDEX idx_approvals_severity_created ON approvals(severity, created_at) WHERE status = 'pending'; -- Migration 012

-- Dynamic Assignment Adaptation Indexes
CREATE INDEX idx_approvals_adaptive ON approvals(is_adaptive_exception) WHERE is_adaptive_exception = true;
CREATE INDEX idx_approvals_adaptation_strategy ON approvals(adaptation_strategy) WHERE adaptation_strategy IS NOT NULL;
CREATE INDEX idx_approvals_adaptation_confidence ON approvals(adaptation_confidence) WHERE adaptation_confidence IS NOT NULL;
CREATE INDEX idx_approvals_auto_approved ON approvals(auto_approved) WHERE auto_approved = true;
CREATE INDEX idx_approvals_suggested_assignment ON approvals(suggested_assignment_id) WHERE suggested_assignment_id IS NOT NULL;
CREATE INDEX idx_approvals_adaptive_metadata_gin ON approvals USING gin(adaptation_metadata) WHERE adaptation_metadata IS NOT NULL;

-- Scan logs (with all performance optimizations)
CREATE INDEX idx_scans_trip ON scan_logs(trip_log_id);
CREATE INDEX idx_scans_timestamp ON scan_logs(scan_timestamp);
CREATE INDEX idx_scans_type ON scan_logs(scan_type);
CREATE INDEX idx_scans_location ON scan_logs(scanned_location_id);
CREATE INDEX idx_scans_user_valid_timestamp ON scan_logs(scanner_user_id, is_valid, scan_timestamp); -- Migration 008
CREATE INDEX idx_scans_trip_type ON scan_logs(trip_log_id, scan_type) WHERE trip_log_id IS NOT NULL; -- Migration 008
CREATE INDEX idx_scan_logs_timestamp_user ON scan_logs(scan_timestamp DESC, scanner_user_id); -- Migration 012-- =======
=====================================================================
-- Shift Management Table Indexes
-- ============================================================================

-- Driver shifts
CREATE INDEX idx_driver_shifts_truck ON driver_shifts(truck_id);
CREATE INDEX idx_driver_shifts_driver ON driver_shifts(driver_id);
CREATE INDEX idx_driver_shifts_date ON driver_shifts(shift_date);
CREATE INDEX idx_driver_shifts_status ON driver_shifts(status);
CREATE INDEX idx_driver_shifts_truck_date ON driver_shifts(truck_id, shift_date);
CREATE INDEX idx_driver_shifts_driver_date ON driver_shifts(driver_id, shift_date);
CREATE INDEX idx_driver_shifts_active_status ON driver_shifts(status, shift_date) WHERE status = 'active';
CREATE INDEX idx_driver_shifts_assignment ON driver_shifts(assignment_id) WHERE assignment_id IS NOT NULL;
CREATE INDEX idx_driver_shifts_recurrence ON driver_shifts(recurrence_pattern) WHERE recurrence_pattern IS NOT NULL;

-- Shift handovers
CREATE INDEX idx_shift_handovers_from ON shift_handovers(from_shift_id);
CREATE INDEX idx_shift_handovers_to ON shift_handovers(to_shift_id);
CREATE INDEX idx_shift_handovers_time ON shift_handovers(handover_time);
CREATE INDEX idx_shift_handovers_completed_by ON shift_handovers(completed_by);--
 ============================================================================
-- System Monitoring Table Indexes
-- ============================================================================

-- System logs
CREATE INDEX idx_system_logs_type ON system_logs(log_type);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX idx_system_logs_user ON system_logs(user_id);
CREATE INDEX idx_system_logs_type_created ON system_logs(log_type, created_at);
CREATE INDEX idx_system_logs_details_gin ON system_logs USING gin(details) WHERE details IS NOT NULL;

-- System tasks
CREATE INDEX idx_system_tasks_type ON system_tasks(type);
CREATE INDEX idx_system_tasks_status ON system_tasks(status);
CREATE INDEX idx_system_tasks_priority ON system_tasks(priority);
CREATE INDEX idx_system_tasks_scheduled ON system_tasks(scheduled_for);
CREATE INDEX idx_system_tasks_created_by ON system_tasks(created_by);
CREATE INDEX idx_system_tasks_status_priority ON system_tasks(status, priority) WHERE status = 'pending';
CREATE INDEX idx_system_tasks_auto_executable ON system_tasks(auto_executable) WHERE auto_executable = true;
CREATE INDEX idx_system_tasks_metadata_gin ON system_tasks USING gin(metadata) WHERE metadata IS NOT NULL;

-- System health logs
CREATE INDEX idx_system_health_logs_check_type ON system_health_logs(check_type);
CREATE INDEX idx_system_health_logs_status ON system_health_logs(status);
CREATE INDEX idx_system_health_logs_metric ON system_health_logs(metric_name);
CREATE INDEX idx_system_health_logs_timestamp ON system_health_logs(check_timestamp);
CREATE INDEX idx_system_health_logs_status_timestamp ON system_health_logs(status, check_timestamp) WHERE status != 'healthy';
CREATE INDEX idx_system_health_logs_details_gin ON system_health_logs USING gin(details) WHERE details IS NOT NULL;

-- Automated fix logs
CREATE INDEX idx_automated_fix_logs_type ON automated_fix_logs(fix_type);
CREATE INDEX idx_automated_fix_logs_entity ON automated_fix_logs(entity_type, entity_id);
CREATE INDEX idx_automated_fix_logs_applied_at ON automated_fix_logs(applied_at);
CREATE INDEX idx_automated_fix_logs_success ON automated_fix_logs(success);
CREATE INDEX idx_automated_fix_logs_details_gin ON automated_fix_logs USING gin(details) WHERE details IS NOT NULL;