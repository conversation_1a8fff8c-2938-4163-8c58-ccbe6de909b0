-- Migration: Fix approvals table schema to match server code expectations (Corrected)
-- Date: 2025-01-03
-- Purpose: Fix 500 errors caused by missing columns in approvals table

-- This script will be executed manually so we can see any errors

-- Add severity column if it doesn't exist
ALTER TABLE approvals ADD COLUMN IF NOT EXISTS severity VARCHAR(20) DEFAULT 'medium' 
CHECK (severity IN ('low', 'medium', 'high', 'critical'));

-- Set default severity for existing records
UPDATE approvals SET severity = 'medium' WHERE severity IS NULL;

-- Add reported_by column if it doesn't exist  
ALTER TABLE approvals ADD COLUMN IF NOT EXISTS reported_by INTEGER REFERENCES users(id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_approvals_severity ON approvals(severity);
CREATE INDEX IF NOT EXISTS idx_approvals_reported_by ON approvals(reported_by);

-- Verify the changes
DO $$
DECLARE
    col_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO col_count
    FROM information_schema.columns 
    WHERE table_name = 'approvals' 
    AND column_name IN ('severity', 'reported_by', 'exception_description');
    
    IF col_count >= 2 THEN
        RAISE NOTICE 'SUCCESS: All required columns are now present';
    ELSE
        RAISE NOTICE 'WARNING: Only % out of required columns found', col_count;
    END IF;
END $$;
