#!/bin/bash

# Test script for component detection functionality
# This script tests the component detection functions without requiring root privileges

# Source the logging functions and component detection from the main script
source <(grep -A 50 "^log_debug()" deploy-hauling-qr-ubuntu.sh)
source <(grep -A 200 "^# Global variables to track component status" deploy-hauling-qr-ubuntu.sh | head -n 200)

# Initialize logging variables for testing
LOG_FILE="/tmp/test-component-detection.log"
current_step="testing"
CURRENT_LOG_LEVEL=0

# Test the component detection functions
echo "Testing Component Detection Functions"
echo "====================================="

# Test version comparison function
echo "Testing version comparison..."
if version_compare "18.1.0" "18.0.0"; then
    echo "✓ Version comparison test 1 passed (18.1.0 >= 18.0.0)"
else
    echo "✗ Version comparison test 1 failed"
fi

if version_compare "17.9.0" "18.0.0"; then
    echo "✗ Version comparison test 2 failed (17.9.0 should be < 18.0.0)"
else
    echo "✓ Version comparison test 2 passed (17.9.0 < 18.0.0)"
fi

# Test component detection
echo -e "\nTesting component detection..."
detect_all_components

echo -e "\nComponent detection test completed!"
echo "Check the log file at: $LOG_FILE"