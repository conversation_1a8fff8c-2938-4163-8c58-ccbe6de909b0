/**
 * Setup Test Database for init.sql Validation
 * 
 * This script creates a test database for validating the new init.sql file.
 * It creates a new database with "_test" suffix and runs the init.sql file against it.
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env') });

// Database connection configuration for admin operations
const adminConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: 'postgres', // Connect to default postgres database for admin operations
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD
};

// Test database name
const testDbName = `${process.env.DB_NAME}_test`;

async function setupTestDatabase() {
  const adminPool = new Pool(adminConfig);
  
  try {
    console.log('Setting up test database for init.sql validation...');
    
    // Check if test database exists and drop it if it does
    console.log(`Checking if test database "${testDbName}" exists...`);
    const dbCheckResult = await adminPool.query(
      `SELECT 1 FROM pg_database WHERE datname = $1`,
      [testDbName]
    );
    
    if (dbCheckResult.rows.length > 0) {
      console.log(`Test database "${testDbName}" exists. Dropping it...`);
      // Terminate all connections to the database before dropping
      await adminPool.query(`
        SELECT pg_terminate_backend(pg_stat_activity.pid)
        FROM pg_stat_activity
        WHERE pg_stat_activity.datname = $1
          AND pid <> pg_backend_pid()
      `, [testDbName]);
      
      await adminPool.query(`DROP DATABASE ${testDbName}`);
      console.log(`Test database "${testDbName}" dropped.`);
    }
    
    // Create test database
    console.log(`Creating test database "${testDbName}"...`);
    await adminPool.query(`CREATE DATABASE ${testDbName}`);
    console.log(`Test database "${testDbName}" created.`);
    
    // Close admin connection
    await adminPool.end();
    
    // Connect to the test database
    const testConfig = {
      ...adminConfig,
      database: testDbName
    };
    const testPool = new Pool(testConfig);
    
    // Read init.sql file
    console.log('Reading init.sql file...');
    const initSqlPath = path.join(__dirname, 'init.sql');
    const initSql = fs.readFileSync(initSqlPath, 'utf8');
    
    // Execute init.sql on test database
    console.log('Executing init.sql on test database...');
    await testPool.query(initSql);
    console.log('init.sql executed successfully on test database.');
    
    // Close test database connection
    await testPool.end();
    
    console.log(`Test database "${testDbName}" is ready for validation.`);
    console.log(`You can connect to it using: psql -h ${process.env.DB_HOST} -U ${process.env.DB_USER} -d ${testDbName}`);
    
    return {
      success: true,
      message: `Test database "${testDbName}" created and initialized successfully.`
    };
  } catch (error) {
    console.error('Error setting up test database:', error);
    return {
      success: false,
      message: `Failed to set up test database: ${error.message}`,
      error
    };
  } finally {
    // Ensure admin pool is closed
    try {
      await adminPool.end();
    } catch (e) {
      // Ignore errors during cleanup
    }
  }
}

// Execute if run directly
if (require.main === module) {
  setupTestDatabase()
    .then(result => {
      if (result.success) {
        console.log('SUCCESS:', result.message);
        process.exit(0);
      } else {
        console.error('ERROR:', result.message);
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('FATAL ERROR:', err);
      process.exit(1);
    });
} else {
  // Export for use as a module
  module.exports = setupTestDatabase;
}