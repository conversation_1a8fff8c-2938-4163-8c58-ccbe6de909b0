-- Migration: Fix night shift completion logic in schedule_auto_activation
-- Purpose: Align completion logic with evaluate_shift_status function
-- Created: 2025-07-16

CREATE OR REPLACE FUNCTION schedule_auto_activation() RETURNS void AS $$
DECLARE
    activated_count INTEGER := 0;
    completed_count INTEGER := 0;
    shift_record RECORD;
    calculated_status TEXT;
BEGIN
    -- Auto-activate scheduled shifts that should be starting now
    UPDATE driver_shifts
    SET status = 'active', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'scheduled'
        AND (
            (recurrence_pattern = 'single' AND CURRENT_DATE BETWEEN start_date AND end_date)
            OR
            (recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN start_date AND end_date)
        )
        AND (
            -- For regular shifts (same day)
            (end_time >= start_time AND CURRENT_TIME >= start_time AND CURRENT_TIME < end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (end_time < start_time AND (CURRENT_TIME >= start_time OR CURRENT_TIME < end_time))
        );

    GET DIAGNOSTICS activated_count = ROW_COUNT;

    -- Auto-complete active shifts using the same logic as evaluate_shift_status
    FOR shift_record IN
        SELECT id FROM driver_shifts WHERE status = 'active'
    LOOP
        calculated_status := evaluate_shift_status(shift_record.id, CURRENT_TIMESTAMP);
        
        IF calculated_status = 'completed' THEN
            UPDATE driver_shifts
            SET status = 'completed', updated_at = CURRENT_TIMESTAMP
            WHERE id = shift_record.id;
            
            completed_count := completed_count + 1;
        END IF;
    END LOOP;

    -- Log the operation
    RAISE NOTICE 'Auto-activation completed: % activated, % completed', activated_count, completed_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION schedule_auto_activation() IS 
'Automatically activates scheduled shifts and completes active shifts using unified logic with evaluate_shift_status';