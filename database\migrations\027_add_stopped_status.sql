-- Migration: Add stopped status to trip_status enum
-- Description: Adds 'stopped' status to trip_status enum
-- Version: 027
-- Date: 2025-07-09

-- Add 'stopped' to the trip_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'stopped' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
    ) THEN
        ALTER TYPE trip_status ADD VALUE 'stopped';
        RAISE NOTICE 'Added stopped status to trip_status enum';
    ELSE
        RAISE NOTICE 'Stopped status already exists in trip_status enum';
    END IF;
END $$;

-- Verify the migration
DO $$
BEGIN
    -- Check if stopped status exists
    IF EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'stopped' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
    ) THEN
        RAISE NOTICE 'Migration verification: stopped status successfully added to enum';
    ELSE
        RAISE EXCEPTION 'Migration verification failed: stopped status not found in enum';
    END IF;
END $$;
