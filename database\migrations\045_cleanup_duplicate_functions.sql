-- Migration: Cleanup duplicate functions
-- Purpose: Remove duplicate function definitions
-- Created: 2025-07-16

-- Drop all versions of the function to start clean
DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER, TIMESTAMP);
DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER);
DROP FUNCTION IF EXISTS schedule_auto_activation();

-- Create the definitive shift status evaluation function
CREATE OR REPLACE FUNCTION evaluate_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS TEXT AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
    v_new_status TEXT;
BEGIN
    -- Get shift details
    SELECT
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN 'error';
    END IF;

    -- Never override completed or cancelled status (immutable)
    IF v_shift.status IN ('completed', 'cancelled') THEN
        RETURN v_shift.status;
    END IF;

    -- Extract current date and time
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if overnight shift
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- Date range check: current date must be within shift date range
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Time window check
    IF v_is_overnight THEN
        -- Night shift: active if time >= start_time OR time <= end_time
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
        
        -- Completion check for overnight: past end_time on next day
        IF v_current_date > v_shift.end_date THEN
            v_is_past_completion := TRUE;
        ELSIF v_current_date = v_shift.end_date THEN
            v_is_past_completion := (v_current_time > v_shift.end_time);
        ELSE
            v_is_past_completion := FALSE;
        END IF;
    ELSE
        -- Day shift: active if time between start_time and end_time
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
        
        -- Completion check for day shift: past end_time on same day
        IF v_current_date > v_shift.end_date THEN
            v_is_past_completion := TRUE;
        ELSIF v_current_date = v_shift.end_date THEN
            v_is_past_completion := (v_current_time > v_shift.end_time);
        ELSE
            v_is_past_completion := FALSE;
        END IF;
    END IF;

    -- Apply status rules in priority order
    IF v_is_past_completion THEN
        v_new_status := 'completed';
    ELSIF v_is_within_date_range AND v_is_within_time_window THEN
        v_new_status := 'active';
    ELSE
        v_new_status := 'scheduled';
    END IF;

    RETURN v_new_status;
END;
$$ LANGUAGE plpgsql;

-- Create simplified auto-activation function
CREATE OR REPLACE FUNCTION schedule_auto_activation() RETURNS void AS $$
DECLARE
    activated_count INTEGER := 0;
    completed_count INTEGER := 0;
    shift_record RECORD;
    calculated_status TEXT;
    current_status TEXT;
BEGIN
    -- Process all non-cancelled shifts
    FOR shift_record IN
        SELECT id, status FROM driver_shifts WHERE status != 'cancelled'
    LOOP
        current_status := shift_record.status;
        calculated_status := evaluate_shift_status(shift_record.id, CURRENT_TIMESTAMP);
        
        -- Update if status should change
        IF calculated_status != current_status AND calculated_status != 'error' THEN
            UPDATE driver_shifts
            SET status = calculated_status::shift_status, updated_at = CURRENT_TIMESTAMP
            WHERE id = shift_record.id;
            
            -- Count transitions
            IF current_status = 'scheduled' AND calculated_status = 'active' THEN
                activated_count := activated_count + 1;
            ELSIF current_status = 'active' AND calculated_status = 'completed' THEN
                completed_count := completed_count + 1;
            END IF;
        END IF;
    END LOOP;

    RAISE NOTICE 'Auto-activation completed: % activated, % completed', activated_count, completed_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION evaluate_shift_status(INTEGER, TIMESTAMP) IS 
'Evaluates correct shift status based on date/time rules for day and night shifts';

COMMENT ON FUNCTION schedule_auto_activation() IS 
'Auto-activates and completes shifts based on current date/time';