-- ============================================================================
-- Migration: Fix shift_date to allow NULL for multi-day shifts
-- Purpose: Remove NOT NULL constraint from shift_date to support multi-day shifts
-- Date: 2025-07-11
-- ============================================================================

-- Remove the NOT NULL constraint from shift_date column
-- This allows multi-day shifts to have NULL shift_date while using start_date/end_date
ALTER TABLE driver_shifts ALTER COLUMN shift_date DROP NOT NULL;

-- Add comment explaining the new behavior
COMMENT ON COLUMN driver_shifts.shift_date IS 
'Date for single-day shifts. NULL for multi-day shifts that use start_date/end_date range.';

-- Verify the constraint change worked
DO $$
DECLARE
    column_nullable BOOLEAN;
BEGIN
    -- Check if shift_date is now nullable
    SELECT is_nullable = 'YES' INTO column_nullable
    FROM information_schema.columns 
    WHERE table_name = 'driver_shifts' 
      AND column_name = 'shift_date'
      AND table_schema = 'public';
    
    IF column_nullable THEN
        RAISE NOTICE 'SUCCESS: shift_date column is now nullable for multi-day shifts';
    ELSE
        RAISE NOTICE 'WARNING: shift_date column is still NOT NULL';
    END IF;
END $$;

-- Test the constraint logic
DO $$
BEGIN
    -- Test that the existing CHECK constraint still works correctly
    RAISE NOTICE 'Testing constraint logic:';
    RAISE NOTICE '- Single shifts (recurrence_pattern=single): shift_date must NOT be NULL';
    RAISE NOTICE '- Multi-day shifts (recurrence_pattern!=single): shift_date can be NULL, start_date/end_date required';
    RAISE NOTICE 'Constraint: (recurrence_pattern = ''single'' AND shift_date IS NOT NULL) OR (recurrence_pattern != ''single'' AND start_date IS NOT NULL AND end_date IS NOT NULL)';
END $$;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 030 completed successfully: shift_date nullable constraint fixed';
    RAISE NOTICE '- Removed NOT NULL constraint from shift_date column';
    RAISE NOTICE '- Multi-day shifts can now have NULL shift_date';
    RAISE NOTICE '- Single-day shifts still require shift_date (enforced by CHECK constraint)';
    RAISE NOTICE '- Multi-day shifts use start_date/end_date range instead';
END $$;
