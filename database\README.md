# Database Management Guide

## Simple Reset Command

**Just run this one command:**
```bash
cd database
node reset.js --force
```

That's it! This will:
- Drop all existing tables
- Create fresh tables with complete schema
- Include all migrations up to 063
- No migration dependency issues

## Usage

```bash
# Reset with confirmation
node reset.js

# Reset without confirmation (recommended)
node reset.js --force

# Show help
node reset.js --help
```

## Files

- **`reset.js`** - The ONLY file you need to run for fresh database tables
- **`init.sql`** - Contains the complete consolidated schema (Version 4.0, July 2025)
- **`run-migration.js`** - Your existing migration runner (not needed for reset)

## Recent Updates

### July 2025 Update (Version 4.0)
The `init.sql` file has been updated to version 4.0, incorporating all migrations up to 063. This update includes:

- **Complete Schema**: All tables, functions, triggers, and other database objects
- **Shift Management System**: Full implementation of driver shifts and handovers
- **System Health Monitoring**: Comprehensive system health tracking
- **Multi-Location Workflow**: Support for complex trip routes
- **Dynamic Assignment Adaptation**: Intelligent assignment management
- **Performance Optimizations**: Optimized indexes and query performance

For more details, see the [Database Migration Guide](../docs/DATABASE_MIGRATION_GUIDE.md).