-- Migration: Ensure system_logs table exists
-- Purpose: Create the system_logs table if it doesn't exist

-- Check if the table exists and create it if it doesn't
DO $$
BEGIN
    -- Check if the table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'system_logs'
    ) THEN
        -- Create the table
        CREATE TABLE system_logs (
            id SERIAL PRIMARY KEY,
            log_type VARCHAR(50) NOT NULL,
            message TEXT NOT NULL,
            details JSON<PERSON>,
            user_id INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create indexes
        CREATE INDEX idx_system_logs_log_type ON system_logs(log_type);
        CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
        
        -- Add comments
        COMMENT ON TABLE system_logs IS 'System-wide logging for automated and manual operations';
        COMMENT ON COLUMN system_logs.log_type IS 'Type of log entry (e.g., SHIFT_AUTO_ACTIVATION, SHIFT_MANUAL_COMPLETION)';
        COMMENT ON COLUMN system_logs.details IS 'Additional details in JSON format';
        
        RAISE NOTICE 'Created system_logs table';
    ELSE
        RAISE NOTICE 'system_logs table already exists';
    END IF;
END;
$$;