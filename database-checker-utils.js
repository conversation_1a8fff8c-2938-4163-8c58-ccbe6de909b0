require('dotenv').config();
const { Pool } = require('pg');

/**
 * Shared database utilities for migration and function checking
 */
class DatabaseChecker {
  constructor() {
    this.pool = null;
  }

  /**
   * Validate required environment variables
   * @param {string[]} additionalVars - Additional variables to validate
   */
  static validateConfig(additionalVars = []) {
    const required = ['DB_USER', 'DB_HOST', 'DB_NAME', 'DB_PASSWORD', 'DB_PORT', ...additionalVars];
    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
  }

  /**
   * Initialize database connection pool
   */
  connect() {
    if (!this.pool) {
      this.pool = new Pool({
        user: process.env.DB_USER,
        host: process.env.DB_HOST,
        database: process.env.DB_NAME,
        password: process.env.DB_PASSWORD,
        port: process.env.DB_PORT,
      });
    }
    return this.pool;
  }

  /**
   * Close database connection
   */
  async disconnect() {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }
  }

  /**
   * Execute multiple database operations with proper error handling
   * @param {Array<Function>} operations - Array of async functions that return promises
   * @returns {Array} Results with status and value/reason
   */
  async executeParallel(operations) {
    const results = await Promise.allSettled(operations.map(op => op()));
    return results.map((result, index) => ({
      index,
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null
    }));
  }

  /**
   * Format console output with consistent styling
   * @param {string} level - log level (info, success, warning, error)
   * @param {string} message - message to display
   * @param {number} indent - indentation level
   */
  static log(level, message, indent = 0) {
    const icons = {
      info: '🔍',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      tool: '🔧'
    };
    
    const spaces = '  '.repeat(indent);
    console.log(`${spaces}${icons[level] || '📋'} ${message}`);
  }
}

module.exports = { DatabaseChecker };