# System Health Monitoring Guide

## Overview

The System Health Monitoring feature provides comprehensive monitoring and maintenance capabilities for the Hauling QR Trip Management System. It offers real-time health status monitoring, automated issue detection, one-click fixes, and proactive maintenance recommendations across all system modules.

## Features

### Core Monitoring Modules

1. **Shift Management Health**
   - Monitors shift transitions and status consistency
   - Detects orphaned or stuck shifts
   - Validates shift assignment integrity

2. **Assignment Management Health**
   - Tracks assignment synchronization with active shifts
   - Identifies unassigned drivers or trucks
   - Monitors assignment workflow integrity

3. **Trip Monitoring Health**
   - Validates trip workflow states and transitions
   - Detects incomplete or stalled trips
   - Monitors driver assignment consistency

4. **Database Health**
   - Connection pool monitoring
   - Query performance tracking
   - Table statistics and index usage analysis
   - Dead tuple ratio monitoring

### Mobile-First Design & Accessibility

The System Health Monitor is designed with mobile-first principles and comprehensive accessibility support:

#### Mobile Responsiveness
- **Responsive Layout**: Adapts from single-column mobile to multi-column desktop layout
- **Touch-Friendly Controls**: All interactive elements have minimum 44px touch targets
- **Collapsible Sections**: Module details can be expanded/collapsed to save screen space
- **Optimized Typography**: Responsive text sizing (base/sm on mobile, lg/xl on desktop)
- **Flexible Grid**: Uses CSS Grid with responsive breakpoints (sm:, md:)

#### Accessibility Features
- **ARIA Labels**: Comprehensive labeling for screen readers
- **Keyboard Navigation**: Full keyboard support with Enter/Space key handling
- **Focus Management**: Visible focus indicators and proper tab order
- **Screen Reader Support**: Semantic HTML with proper roles and descriptions
- **Status Announcements**: Live regions for dynamic content updates
- **High Contrast**: Proper color contrast ratios for text readability

## User Interface

### Main Dashboard

The System Health Monitor is accessible through the Settings page:

1. Navigate to **Settings** → **System Health Monitor**
2. View real-time status for all four modules
3. Use the **Auto-refresh** toggle to enable/disable automatic updates (30-second interval)
4. Click **Refresh Status** to manually update all module statuses

### Module Status Display

Each module shows:
- **Status Icon**: ✅ Operational, ⚠️ Issues Detected, ❌ Critical
- **Module Name**: Shifts, Assignments, Trips, Database
- **Status Badge**: Color-coded status indicator
- **Expand/Collapse**: Click module header to view detailed information
- **Fix Button**: One-click automated fixes for detected issues

### Issue Details

When issues are detected, each module displays:
- **Issue Type**: Categorized problem description
- **Severity Level**: Critical, Medium, or Low priority
- **Description**: Detailed explanation of the issue
- **Affected Records**: Count of impacted database records
- **Auto-fixable**: Indicates if automated fixes are available

## Task Management System

### Task Overview

The integrated Task Management Panel provides:
- **Active Tasks**: Current maintenance and monitoring tasks
- **Task History**: Completed and failed task records
- **Recommendations**: System-generated maintenance suggestions
- **Scheduling**: Automated task scheduling capabilities

### Task Types

1. **Monitoring Tasks**
   - Health check validations
   - Performance monitoring
   - Data integrity checks

2. **Maintenance Tasks**
   - Database optimization
   - Cache clearing
   - Log rotation

3. **Fix Tasks**
   - Automated issue resolution
   - Data synchronization
   - Workflow corrections

### Task Management Interface

#### Creating Tasks
1. Click **Create Task** in the Task Management Panel
2. Select task type and priority level
3. Set description and scheduling options
4. Save to add to the task queue

#### Managing Tasks
- **Start Task**: Begin execution of pending tasks
- **Complete Task**: Mark tasks as successfully completed
- **Fail Task**: Mark tasks as failed with error details
- **Delete Task**: Remove tasks from the system

## System Cleanup Management

### Cleanup Analysis

The Cleanup Management Panel provides:
- **Code Analysis**: Scans JavaScript files for unused functions
- **Safety Checks**: Preserves critical system functions
- **Backup Creation**: Automatic backups before cleanup operations
- **Rollback Capability**: Restore previous state if needed

### Cleanup Process

1. **Analysis Phase**
   - Scan server and script files
   - Identify unused functions
   - Generate safety report

2. **Review Phase**
   - Review analysis results
   - Confirm cleanup operations
   - Set safety parameters

3. **Execution Phase**
   - Create system backup
   - Remove unused code
   - Verify system integrity

4. **Verification Phase**
   - Test system functionality
   - Monitor for issues
   - Rollback if necessary

## API Endpoints

### Health Status Endpoints

#### Get System Health Status
```
GET /api/system-health/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "shifts": {
      "status": "operational|warning|critical",
      "issues": [...],
      "lastCheck": "2024-01-15T10:30:00Z"
    },
    "assignments": { ... },
    "trips": { ... },
    "database": { ... }
  }
}
```

### Fix Endpoints

#### Fix Module Issues
```
POST /api/system-health/fix-{module}
Authorization: Bearer <token>
```

Available modules: `shifts`, `assignments`, `trips`, `database`

**Response:**
```json
{
  "success": true,
  "data": {
    "fixedIssues": 3,
    "affectedRecords": 15,
    "executionTime": "2.3s",
    "details": [...]
  }
}
```

### Task Management Endpoints

#### Get Tasks
```
GET /api/tasks
Authorization: Bearer <token>
```

#### Create Task
```
POST /api/tasks
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Database Optimization",
  "description": "Optimize database performance",
  "type": "maintenance",
  "priority": "medium",
  "scheduledFor": "2024-01-15T14:00:00Z"
}
```

#### Update Task Status
```
PUT /api/tasks/:id/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "completed|failed|in_progress",
  "notes": "Task completion notes"
}
```

## Troubleshooting

### Common Issues

#### API Connection Errors
- **Symptom**: "Error fetching health data" messages
- **Solution**: Check server connectivity and authentication token
- **Prevention**: Ensure proper network configuration and token refresh

#### Fix Operations Failing
- **Symptom**: Fix buttons show errors or don't complete
- **Solution**: Check server logs for detailed error information
- **Prevention**: Regular database maintenance and monitoring

#### Mobile Interface Issues
- **Symptom**: Interface elements too small or difficult to interact with
- **Solution**: Ensure proper viewport settings and touch target sizes
- **Prevention**: Test on actual mobile devices regularly

### Performance Optimization

#### Database Health
- Monitor connection pool usage
- Optimize slow queries
- Regular VACUUM operations for PostgreSQL
- Index usage analysis and optimization

#### Frontend Performance
- Enable auto-refresh only when needed
- Use collapsible sections to reduce initial render load
- Implement proper loading states for better user experience

## Best Practices

### Monitoring Schedule

1. **Real-time Monitoring**: Enable auto-refresh during active operations
2. **Regular Checks**: Manual refresh every 15-30 minutes during business hours
3. **Daily Reviews**: Check task history and recommendations daily
4. **Weekly Maintenance**: Run cleanup analysis and optimization tasks

### Mobile Usage

1. **Touch Targets**: All interactive elements meet 44px minimum size
2. **One-Handed Operation**: Primary actions accessible with thumb navigation
3. **Orientation Support**: Interface adapts to portrait and landscape modes
4. **Network Awareness**: Graceful handling of poor connectivity

### Accessibility Compliance

1. **Screen Reader Testing**: Regular testing with screen reader software
2. **Keyboard Navigation**: Ensure all functionality accessible via keyboard
3. **Color Contrast**: Maintain WCAG AA compliance for text contrast
4. **Focus Management**: Clear focus indicators and logical tab order

## Security Considerations

### Authentication
- All API endpoints require valid JWT authentication
- Tokens should be refreshed regularly
- Failed authentication attempts are logged

### Authorization
- System health monitoring requires administrative privileges
- Fix operations are restricted to authorized users
- Task management access is role-based

### Data Protection
- Health status data may contain sensitive system information
- Cleanup operations create backups before making changes
- All operations are logged for audit purposes

## Integration with Existing Systems

### Shift Synchronization Monitor
- Complements existing shift monitoring functionality
- Shares common monitoring scripts and database functions
- Provides unified interface for all monitoring activities

### Database Integration
- Uses existing connection pooling and query optimization
- Integrates with current migration system
- Leverages existing database health monitoring scripts

### WebSocket Integration
- Real-time updates through existing WebSocket infrastructure
- Efficient data synchronization with dashboard components
- Minimal impact on system performance

## Support and Maintenance

### Regular Maintenance Tasks

1. **Daily**
   - Review system health status
   - Check for critical issues
   - Monitor task completion

2. **Weekly**
   - Run cleanup analysis
   - Review performance metrics
   - Update task schedules

3. **Monthly**
   - Database optimization
   - Log rotation and cleanup
   - System performance review

### Getting Help

For technical support or questions about the System Health Monitoring feature:

1. Check the troubleshooting section above
2. Review server logs for detailed error information
3. Consult the API documentation for endpoint specifications
4. Contact system administrators for access or permission issues

## Version History

- **v1.0**: Initial implementation with basic health monitoring
- **v1.1**: Added task management system and automated fixes
- **v1.2**: Implemented cleanup management functionality
- **v1.3**: Added database health monitoring module
- **v1.4**: Enhanced mobile responsiveness and accessibility features