-- Migration: Fix function signatures by dropping and recreating
-- Purpose: Fix functions calling evaluate_shift_status with wrong parameters
-- Created: 2025-07-16

-- Drop the problematic functions
DROP FUNCTION IF EXISTS fix_incorrectly_completed_shifts();
DROP FUNCTION IF EXISTS check_shift_status_consistency();

-- Recreate fix_incorrectly_completed_shifts function with correct signature
CREATE FUNCTION fix_incorrectly_completed_shifts()
RETURNS TABLE (
    shift_id INTEGER,
    old_status shift_status,
    new_status shift_status,
    truck_id INTEGER,
    driver_id INTEGER,
    shift_type shift_type,
    start_date DATE,
    end_date DATE
) AS $$
DECLARE
    v_shift RECORD;
    v_correct_status TEXT;
    v_fixed_count INTEGER := 0;
BEGIN
    -- Find all completed shifts and check if they should actually be completed
    FOR v_shift IN
        SELECT ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status, ds.start_date, ds.end_date
        FROM driver_shifts ds
        WHERE ds.status = 'completed'
    LOOP
        -- Get the correct status using the 2-parameter function
        v_correct_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
        
        -- If the correct status is different from current status, fix it
        IF v_correct_status != v_shift.status::TEXT THEN
            UPDATE driver_shifts
            SET 
                status = v_correct_status::shift_status,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_shift.id;
            
            v_fixed_count := v_fixed_count + 1;
            
            -- Return the fixed shift information
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_correct_status::shift_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Fixed % incorrectly completed shifts', v_fixed_count;
END;
$$ LANGUAGE plpgsql;

-- Recreate check_shift_status_consistency function with correct signature
CREATE FUNCTION check_shift_status_consistency()
RETURNS TABLE (
    shift_id INTEGER,
    current_status shift_status,
    expected_status TEXT,
    truck_id INTEGER,
    driver_id INTEGER,
    shift_type shift_type,
    start_date DATE,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    issue_description TEXT
) AS $$
DECLARE
    v_shift RECORD;
    v_expected_status TEXT;
BEGIN
    -- Check all shifts for status consistency
    FOR v_shift IN
        SELECT 
            ds.id, 
            ds.truck_id, 
            ds.driver_id, 
            ds.shift_type, 
            ds.status, 
            ds.start_date, 
            ds.end_date, 
            ds.start_time, 
            ds.end_time
        FROM driver_shifts ds
        WHERE ds.status IN ('scheduled', 'active', 'completed')
    LOOP
        -- Get the expected status using the 2-parameter function
        v_expected_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
        
        -- If there's a mismatch, report it
        IF v_expected_status != v_shift.status::TEXT THEN
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_expected_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date,
                v_shift.start_time,
                v_shift.end_time,
                CASE 
                    WHEN v_shift.status = 'completed' AND v_expected_status != 'completed' THEN 
                        'Shift incorrectly marked as completed'
                    WHEN v_shift.status = 'scheduled' AND v_expected_status = 'active' THEN 
                        'Shift should be active but is still scheduled'
                    WHEN v_shift.status = 'active' AND v_expected_status = 'completed' THEN 
                        'Shift should be completed but is still active'
                    ELSE 
                        'Status mismatch detected'
                END;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON FUNCTION fix_incorrectly_completed_shifts() IS 
'Fixes shifts that are incorrectly marked as completed - uses 2-parameter evaluate_shift_status';

COMMENT ON FUNCTION check_shift_status_consistency() IS 
'Checks for shift status inconsistencies - uses 2-parameter evaluate_shift_status';