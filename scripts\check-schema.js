#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function checkSchema() {
    try {
        // Check driver_shifts columns
        const columns = await pool.query(`
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'driver_shifts' 
            ORDER BY ordinal_position
        `);
        
        console.log('driver_shifts columns:');
        columns.rows.forEach(c => console.log(`  ${c.column_name}: ${c.data_type} (${c.is_nullable})`));
        
        // Check assignment_status enum
        const assignmentStatus = await pool.query(`
            SELECT unnest(enum_range(NULL::assignment_status)) as status
        `);
        
        console.log('\nassignment_status enum values:');
        assignmentStatus.rows.forEach(s => console.log(`  ${s.status}`));
        
        // Check recurrence_pattern enum
        const recurrencePattern = await pool.query(`
            SELECT unnest(enum_range(NULL::recurrence_pattern)) as pattern
        `);
        
        console.log('\nrecurrence_pattern enum values:');
        recurrencePattern.rows.forEach(p => console.log(`  ${p.pattern}`));
        
        // Check existing truck and driver IDs
        const trucks = await pool.query('SELECT id, truck_number FROM dump_trucks LIMIT 3');
        console.log('\nExisting trucks:');
        trucks.rows.forEach(t => console.log(`  ID: ${t.id}, Number: ${t.truck_number}`));
        
        const drivers = await pool.query('SELECT id, full_name FROM drivers LIMIT 3');
        console.log('\nExisting drivers:');
        drivers.rows.forEach(d => console.log(`  ID: ${d.id}, Name: ${d.full_name}`));
        
    } catch (error) {
        console.error('Error:', error.message);
    } finally {
        await pool.end();
    }
}

checkSchema();