/**
 * Integration tests for system health API endpoints
 * 
 * Tests the API endpoints for system health monitoring and automated fixes
 */

const request = require('supertest');
const express = require('express');
const systemHealthRoutes = require('../../routes/system-health-routes');
const SystemMonitoringService = require('../../services/SystemMonitoringService');
const AutomatedFixService = require('../../services/AutomatedFixService');
const authMiddleware = require('../../middleware/auth-middleware');

// Mock dependencies
jest.mock('../../services/SystemMonitoringService');
jest.mock('../../services/AutomatedFixService');
jest.mock('../../middleware/auth-middleware', () => ({
  authenticateToken: jest.fn((req, res, next) => next()),
  isAdmin: jest.fn((req, res, next) => next())
}));

describe('System Health API Routes', () => {
  let app;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create express app and apply routes
    app = express();
    app.use(express.json());
    app.use('/api/system-health', systemHealthRoutes);
  });
  
  describe('GET /api/system-health/status', () => {
    it('should return system health status', async () => {
      // Mock service response
      const mockHealthStatus = {
        shifts: {
          status: 'operational',
          issues: [],
          lastCheck: new Date().toISOString()
        },
        assignments: {
          status: 'warning',
          issues: [{ type: 'display_mismatch', severity: 'medium' }],
          lastCheck: new Date().toISOString()
        },
        trips: {
          status: 'operational',
          issues: [],
          lastCheck: new Date().toISOString()
        },
        database: {
          status: 'operational',
          issues: [],
          lastCheck: new Date().toISOString()
        },
        overall: {
          status: 'warning',
          issues_count: 1,
          lastCheck: new Date().toISOString()
        }
      };
      
      SystemMonitoringService.prototype.getSystemHealth = jest.fn().mockResolvedValue(mockHealthStatus);
      
      // Make request
      const response = await request(app)
        .get('/api/system-health/status')
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Assertions
      expect(response.body).toHaveProperty('shifts');
      expect(response.body).toHaveProperty('assignments');
      expect(response.body).toHaveProperty('trips');
      expect(response.body).toHaveProperty('database');
      expect(response.body).toHaveProperty('overall');
      expect(response.body.overall.status).toBe('warning');
      
      // Verify service was called
      expect(SystemMonitoringService.prototype.getSystemHealth).toHaveBeenCalled();
    });
    
    it('should handle errors and return 500', async () => {
      // Mock service error
      SystemMonitoringService.prototype.getSystemHealth = jest.fn().mockRejectedValue(
        new Error('Service error')
      );
      
      // Make request
      const response = await request(app)
        .get('/api/system-health/status')
        .expect('Content-Type', /json/)
        .expect(500);
      
      // Assertions
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Error retrieving system health');
    });
  });
  
  describe('POST /api/system-health/fix-shifts', () => {
    it('should fix shift management issues', async () => {
      // Mock service response
      const mockFixResult = {
        success: true,
        message: 'Successfully fixed 2 shift issues',
        timestamp: new Date().toISOString(),
        details: {
          before: { issueCount: 2 },
          after: { issueCount: 0 }
        },
        affectedRecords: ['shift_123', 'shift_124']
      };
      
      AutomatedFixService.prototype.fixShiftManagement = jest.fn().mockResolvedValue(mockFixResult);
      
      // Make request
      const response = await request(app)
        .post('/api/system-health/fix-shifts')
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Assertions
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details.before.issueCount).toBe(2);
      expect(response.body.details.after.issueCount).toBe(0);
      
      // Verify service was called
      expect(AutomatedFixService.prototype.fixShiftManagement).toHaveBeenCalled();
    });
    
    it('should handle errors and return 500', async () => {
      // Mock service error
      AutomatedFixService.prototype.fixShiftManagement = jest.fn().mockRejectedValue(
        new Error('Service error')
      );
      
      // Make request
      const response = await request(app)
        .post('/api/system-health/fix-shifts')
        .expect('Content-Type', /json/)
        .expect(500);
      
      // Assertions
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Error fixing shift management issues');
    });
  });
  
  describe('POST /api/system-health/fix-assignments', () => {
    it('should fix assignment management issues', async () => {
      // Mock service response
      const mockFixResult = {
        success: true,
        message: 'Successfully fixed 2 assignment issues',
        timestamp: new Date().toISOString(),
        details: {
          before: { noActiveShiftCount: 2 },
          after: { noActiveShiftCount: 0 }
        },
        affectedRecords: ['DT-100', 'DT-101']
      };
      
      AutomatedFixService.prototype.fixAssignmentManagement = jest.fn().mockResolvedValue(mockFixResult);
      
      // Make request
      const response = await request(app)
        .post('/api/system-health/fix-assignments')
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Assertions
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details.before.noActiveShiftCount).toBe(2);
      expect(response.body.details.after.noActiveShiftCount).toBe(0);
      
      // Verify service was called
      expect(AutomatedFixService.prototype.fixAssignmentManagement).toHaveBeenCalled();
    });
  });
  
  describe('POST /api/system-health/fix-trips', () => {
    it('should fix trip monitoring issues', async () => {
      // Mock service response
      const mockFixResult = {
        success: true,
        message: 'Successfully fixed 2 trip workflow issues',
        timestamp: new Date().toISOString(),
        details: {
          fixedTrips: [
            { trip_id: 'T-001', previous_status: 'VERIFIED', new_status: 'COMPLETED' },
            { trip_id: 'T-002', previous_status: 'IN_PROGRESS', new_status: 'COMPLETED' }
          ]
        },
        affectedRecords: ['T-001', 'T-002']
      };
      
      AutomatedFixService.prototype.fixTripMonitoring = jest.fn().mockResolvedValue(mockFixResult);
      
      // Make request
      const response = await request(app)
        .post('/api/system-health/fix-trips')
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Assertions
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details.fixedTrips).toHaveLength(2);
      
      // Verify service was called
      expect(AutomatedFixService.prototype.fixTripMonitoring).toHaveBeenCalled();
    });
  });
  
  describe('POST /api/system-health/fix-database', () => {
    it('should fix database issues', async () => {
      // Mock service response
      const mockFixResult = {
        success: true,
        message: 'Successfully optimized database',
        timestamp: new Date().toISOString(),
        details: {
          vacuumed_tables: ['trips', 'shifts'],
          reindexed_tables: ['assignments']
        }
      };
      
      AutomatedFixService.prototype.fixDatabaseIssues = jest.fn().mockResolvedValue(mockFixResult);
      
      // Make request
      const response = await request(app)
        .post('/api/system-health/fix-database')
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Assertions
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details.vacuumed_tables).toContain('trips');
      
      // Verify service was called
      expect(AutomatedFixService.prototype.fixDatabaseIssues).toHaveBeenCalled();
    });
  });
  
  describe('Authentication middleware', () => {
    it('should use authentication middleware for all routes', async () => {
      // Make a request to trigger middleware
      await request(app).get('/api/system-health/status');
      
      // Verify middleware was called
      expect(authMiddleware.authenticateToken).toHaveBeenCalled();
    });
    
    it('should require admin privileges for fix endpoints', async () => {
      // Make a request to trigger middleware
      await request(app).post('/api/system-health/fix-shifts');
      
      // Verify middleware was called
      expect(authMiddleware.isAdmin).toHaveBeenCalled();
    });
  });
});