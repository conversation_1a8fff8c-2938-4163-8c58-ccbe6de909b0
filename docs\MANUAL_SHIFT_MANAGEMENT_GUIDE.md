# Manual Shift Management Guide

## Overview

The Manual Shift Management feature provides administrators with direct control over shift status transitions, addressing the issue where shifts were being automatically completed even when the current date/time hadn't reached the shift's end date or end time.

This guide explains how to use the Manual Shift Management interface in the Settings page to view, complete, and cancel shifts, as well as force refresh shift statuses.

## Key Features

- **View Active and Scheduled Shifts**: See all currently active and scheduled shifts in a tabular format
- **Manual Shift Completion**: Manually mark active shifts as completed when appropriate
- **Shift Cancellation**: Cancel active or scheduled shifts that should not proceed
- **Force Status Refresh**: Synchronize shift statuses between 'scheduled' and 'active' based on current time
- **Status Summary**: View counts of shifts by status (active, scheduled, completed, cancelled)

## Status Transition Rules

The system now follows these strict rules for shift status transitions:

1. **Automatic Transitions**:
   - 'scheduled' → 'active' (when current time enters shift hours)
   - 'active' → 'scheduled' (when current time exits shift hours but still within shift dates)

2. **Manual-Only Transitions**:
   - 'active' → 'completed' (requires administrator action)
   - 'active' → 'cancelled' (requires administrator action)
   - 'scheduled' → 'cancelled' (requires administrator action)

3. **Immutable States**:
   - Once a shift is 'completed' or 'cancelled', its status cannot change

## Using the Manual Shift Management Interface

### Accessing the Interface

1. Navigate to the **Settings** page
2. Click on the **Manual Shift Management** card
3. The interface will load with the current shift status summary and tabs for Active and Scheduled shifts

### Viewing Shifts

- **Active Shifts**: Click the "Active Shifts" tab to view all currently active shifts
- **Scheduled Shifts**: Click the "Scheduled Shifts" tab to view all scheduled shifts
- Each shift displays:
  - Driver name
  - Truck number
  - Shift type (Day/Night)
  - Start date/time
  - End date/time
  - Available actions

### Completing a Shift

1. In the Active Shifts tab, find the shift you want to complete
2. Click the "Complete" button for that shift
3. In the confirmation dialog, enter any completion notes (optional)
4. Click "Complete Shift" to confirm
5. The shift will be marked as completed and removed from the Active Shifts list

### Cancelling a Shift

1. Find the shift you want to cancel (in either Active or Scheduled tab)
2. Click the "Cancel" button for that shift
3. In the confirmation dialog, enter a reason for cancellation (required)
4. Click "Cancel Shift" to confirm
5. The shift will be marked as cancelled and removed from the list

### Refreshing Shift Statuses

1. Click the "Refresh Statuses" button at the top of the interface
2. The system will:
   - Update all shifts between 'scheduled' and 'active' based on current time
   - NOT change any 'completed' or 'cancelled' shifts
   - Update the status summary and shift lists

## Technical Implementation

### Database Functions

The system uses three key database functions with modified behavior:

1. **evaluate_shift_status(shift_id, reference_timestamp)**
   - Evaluates the correct status for a shift based on date/time rules
   - No longer automatically sets shifts to 'completed'
   - Preserves 'completed' status if already set

2. **schedule_auto_activation()**
   - Updates shift statuses between 'scheduled' and 'active' only
   - Skips shifts that are already 'completed' or 'cancelled'
   - Logs all status changes for audit purposes

3. **update_all_shift_statuses(reference_timestamp)**
   - Batch updates all shift statuses with statistics
   - Only transitions between 'scheduled' and 'active'
   - Returns counts of shifts by status

### API Endpoints

The following API endpoints support the Manual Shift Management feature:

- `GET /api/manual-shift-management/active` - Get all active shifts
- `GET /api/manual-shift-management/scheduled` - Get all scheduled shifts
- `POST /api/manual-shift-management/complete/:id` - Manually complete a shift
- `POST /api/manual-shift-management/cancel/:id` - Cancel a shift
- `POST /api/manual-shift-management/refresh` - Force refresh shift statuses
- `GET /api/manual-shift-management/summary` - Get shift status summary

## Troubleshooting

### Common Issues

1. **Shift Not Appearing in Active Tab**
   - Check if the shift's start time has been reached
   - Verify the shift is not already completed or cancelled
   - Try using the "Refresh Statuses" button

2. **Cannot Complete a Shift**
   - Only active shifts can be completed
   - Verify you have administrator privileges
   - Check for any error messages in the response

3. **Shift Status Not Updating**
   - Use the "Refresh Statuses" button to force an update
   - Check if the shift dates and times are correctly set
   - Verify database connectivity

### Audit Logging

All manual shift status changes are logged in the `system_logs` table with:
- The user who made the change
- Previous and new status
- Timestamp of the change
- Additional notes or reasons

## Best Practices

1. **Regular Status Checks**: Periodically use the "Refresh Statuses" button to ensure shifts are properly activated based on current time

2. **Timely Completion**: Complete shifts promptly after they end to maintain accurate records

3. **Detailed Notes**: Provide clear notes when completing or cancelling shifts for audit purposes

4. **Verification**: After completing or cancelling shifts, verify the status summary updates correctly

5. **Coordination**: Coordinate with dispatchers when manually changing shift statuses to avoid operational confusion