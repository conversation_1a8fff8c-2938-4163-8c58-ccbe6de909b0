-- Migration: Add sample shift data
-- Purpose: Add sample data to the driver_shifts table for testing

-- Add sample data if the table is empty
DO $$
DECLARE
    shift_count INTEGER;
BEGIN
    -- Check if the driver_shifts table exists and is empty
    SELECT COUNT(*) INTO shift_count FROM driver_shifts;
    
    IF shift_count = 0 THEN
        -- Create sample drivers if they don't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'drivers'
        ) THEN
            -- Create the drivers table
            CREATE TABLE drivers (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            -- Insert sample drivers
            INSERT INTO drivers (name) VALUES
                ('<PERSON>'),
                ('<PERSON>'),
                ('<PERSON>'),
                ('<PERSON><PERSON>');
                
            RAISE NOTICE 'Created drivers table with sample data';
        END IF;
        
        -- Create sample dump_trucks if they don't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'dump_trucks'
        ) THEN
            -- Create the dump_trucks table
            CREATE TABLE dump_trucks (
                id SERIAL PRIMARY KEY,
                truck_number VARCHAR(20) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            -- Insert sample dump_trucks
            INSERT INTO dump_trucks (truck_number) VALUES
                ('DT-100'),
                ('DT-101'),
                ('DT-102'),
                ('DT-103');
                
            RAISE NOTICE 'Created dump_trucks table with sample data';
        END IF;
        
        -- Insert sample shifts
        INSERT INTO driver_shifts (
            driver_id,
            truck_id,
            shift_type,
            status,
            start_date,
            end_date,
            start_time,
            end_time
        ) VALUES
            -- Active day shift
            (1, 1, 'day', 'active', CURRENT_DATE, CURRENT_DATE, '06:00:00', '18:00:00'),
            
            -- Active night shift
            (2, 2, 'night', 'active', CURRENT_DATE, CURRENT_DATE + 1, '18:00:00', '06:00:00'),
            
            -- Scheduled day shift for tomorrow
            (3, 3, 'day', 'scheduled', CURRENT_DATE + 1, CURRENT_DATE + 1, '06:00:00', '18:00:00'),
            
            -- Scheduled night shift for tomorrow
            (4, 4, 'night', 'scheduled', CURRENT_DATE + 1, CURRENT_DATE + 2, '18:00:00', '06:00:00'),
            
            -- Completed day shift from yesterday
            (1, 1, 'day', 'completed', CURRENT_DATE - 1, CURRENT_DATE - 1, '06:00:00', '18:00:00'),
            
            -- Cancelled night shift from yesterday
            (2, 2, 'night', 'cancelled', CURRENT_DATE - 1, CURRENT_DATE, '18:00:00', '06:00:00');
            
        RAISE NOTICE 'Added sample shift data';
    ELSE
        RAISE NOTICE 'driver_shifts table already has data';
    END IF;
END;
$$;