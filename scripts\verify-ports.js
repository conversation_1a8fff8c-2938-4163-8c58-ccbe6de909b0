#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function verifySystem() {
    console.log('🎯 Final Verification...\n');
    
    try {
        // Test auto-activation
        await pool.query('SELECT schedule_auto_activation()');
        console.log('✅ Auto-activation function works');
        
        // Get current status
        const status = await pool.query(`
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
                COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
            FROM driver_shifts 
            WHERE status != 'cancelled'
        `);
        
        console.log('📊 Current Shift Status:');
        const s = status.rows[0];
        console.log(`   Total: ${s.total}`);
        console.log(`   Active: ${s.active}`);
        console.log(`   Scheduled: ${s.scheduled}`);
        console.log(`   Completed: ${s.completed}`);
        
        console.log('\n🎉 SUCCESS! Your shift management system is fully operational!');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    verifySystem();
}

module.exports = { verifySystem };