-- ============================================================================
-- Migration 063: Add exception_triggered to trip_status enum
-- Purpose: Add missing exception_triggered status that is referenced in trucks.js
-- Date: 2025-07-18
-- ============================================================================

-- Log the migration start
DO $$
BEGIN
    RAISE NOTICE 'Starting trip status update: adding exception_triggered';
END $$;

-- ============================================================================
-- Step 1: Add 'exception_triggered' to existing enum if it doesn't exist
-- ============================================================================

-- Add 'exception_triggered' to the enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum e 
        JOIN pg_type t ON e.enumtypid = t.oid 
        WHERE t.typname = 'trip_status' AND e.enumlabel = 'exception_triggered'
    ) THEN
        ALTER TYPE trip_status ADD VALUE 'exception_triggered';
        RAISE NOTICE 'Added "exception_triggered" to trip_status enum';
    END IF;
END $$;

-- ============================================================================
-- Migration Complete
-- ============================================================================

-- Final success message
DO $$
BEGIN
    RAISE NOTICE 'Migration 063 completed successfully: added exception_triggered to trip_status enum';
END $$;