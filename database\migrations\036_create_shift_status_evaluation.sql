-- Migration: Create comprehensive shift status evaluation function
-- Created: 2025-07-14
-- Purpose: Evaluate and update shift status based on date/time rules

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER, TIMESTAMP);

-- Create the comprehensive shift status evaluation function with enhanced overnight logic
CREATE OR REPLACE FUNCTION evaluate_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS TEXT AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
    v_new_status TEXT;
    v_shift_end_datetime TIMESTAMP;
BEGIN
    -- Get shift details including recurrence pattern support
    SELECT
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status,
        recurrence_pattern
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN 'error';
    END IF;

    -- Never override completed or cancelled status (immutable states)
    IF v_shift.status IN ('completed', 'cancelled') THEN
        RETURN v_shift.status;
    END IF;

    -- Extract current date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if shift spans overnight (night shift logic)
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- Enhanced date range validation with unified approach
    -- All shifts now use start_date/end_date range
    -- Single day shifts: start_date = end_date
    -- Multi-day shifts: start_date < end_date
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Enhanced time window validation with proper overnight logic
    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic for overnight spans
        -- Active when: (current_time >= start_time) OR (current_time <= end_time)
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
    ELSE
        -- Day shift: Use simple BETWEEN logic for same-day shifts
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
    END IF;

    -- Enhanced completion logic with proper overnight handling using unified approach
    IF v_is_overnight THEN
        -- For overnight shifts: completed when we're past end_time on the next day
        -- Calculate the actual end datetime for overnight shifts
        v_shift_end_datetime := (v_shift.end_date + INTERVAL '1 day')::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    ELSE
        -- For day shifts: completed when past end_time on the same day
        v_shift_end_datetime := v_shift.end_date::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    END IF;

    -- Apply status rules in priority order
    IF v_is_past_completion THEN
        -- Rule 3: Completed - past the shift's end datetime
        v_new_status := 'completed';
    ELSIF v_is_within_date_range AND v_is_within_time_window THEN
        -- Rule 1: Active - within date range AND within time window
        v_new_status := 'active';
    ELSIF v_is_within_date_range AND NOT v_is_within_time_window THEN
        -- Rule 2: Scheduled - within date range BUT outside time window
        v_new_status := 'scheduled';
    ELSE
        -- Default: Scheduled for future dates
        v_new_status := 'scheduled';
    END IF;

    RETURN v_new_status;
END;
$$ LANGUAGE plpgsql;

-- Create function to update all shift statuses based on current time with enhanced logic
CREATE OR REPLACE FUNCTION update_all_shift_statuses(
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
RETURNS TABLE (
    updated_count INTEGER,
    activated_count INTEGER,
    completed_count INTEGER,
    scheduled_count INTEGER
) AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_activated_count INTEGER := 0;
    v_completed_count INTEGER := 0;
    v_scheduled_count INTEGER := 0;
    v_shift_record RECORD;
    v_new_status TEXT;
    v_old_status TEXT;
BEGIN
    -- Process all shifts that are not cancelled (immutable state)
    FOR v_shift_record IN
        SELECT id, status, truck_id, driver_id, shift_type
        FROM driver_shifts
        WHERE status != 'cancelled'
        ORDER BY truck_id, start_time -- Process in order for better logging
    LOOP
        v_old_status := v_shift_record.status;

        -- Evaluate new status using enhanced logic
        v_new_status := evaluate_shift_status(v_shift_record.id, p_reference_timestamp);

        -- Update if status has changed and new status is valid
        IF v_new_status != v_old_status AND v_new_status != 'error' THEN
            UPDATE driver_shifts
            SET status = v_new_status::shift_status,
                updated_at = p_reference_timestamp
            WHERE id = v_shift_record.id;

            v_updated_count := v_updated_count + 1;

            -- Track transition types for monitoring
            CASE v_new_status
                WHEN 'active' THEN v_activated_count := v_activated_count + 1;
                WHEN 'completed' THEN v_completed_count := v_completed_count + 1;
                WHEN 'scheduled' THEN v_scheduled_count := v_scheduled_count + 1;
            END CASE;

            -- Log significant transitions for debugging
            IF v_old_status = 'scheduled' AND v_new_status = 'active' THEN
                RAISE NOTICE 'Shift activated: ID=%, Truck=%, Type=%',
                    v_shift_record.id, v_shift_record.truck_id, v_shift_record.shift_type;
            ELSIF v_old_status = 'active' AND v_new_status = 'completed' THEN
                RAISE NOTICE 'Shift completed: ID=%, Truck=%, Type=%',
                    v_shift_record.id, v_shift_record.truck_id, v_shift_record.shift_type;
            END IF;
        END IF;
    END LOOP;

    -- Return detailed statistics
    RETURN QUERY SELECT v_updated_count, v_activated_count, v_completed_count, v_scheduled_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to update specific shift status with enhanced validation
CREATE OR REPLACE FUNCTION update_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
RETURNS TABLE (
    old_status TEXT,
    new_status TEXT,
    status_changed BOOLEAN,
    truck_id INTEGER,
    driver_id INTEGER
) AS $$
DECLARE
    v_new_status TEXT;
    v_old_status TEXT;
    v_truck_id INTEGER;
    v_driver_id INTEGER;
    v_shift_info RECORD;
BEGIN
    -- Get current shift information
    SELECT status, truck_id, driver_id, shift_type
    INTO v_shift_info
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN QUERY SELECT 'shift_not_found'::TEXT, 'error'::TEXT, FALSE, NULL::INTEGER, NULL::INTEGER;
        RETURN;
    END IF;

    v_old_status := v_shift_info.status;
    v_truck_id := v_shift_info.truck_id;
    v_driver_id := v_shift_info.driver_id;

    -- Evaluate new status using enhanced logic
    v_new_status := evaluate_shift_status(p_shift_id, p_reference_timestamp);

    -- Update if status has changed and new status is valid
    IF v_new_status != v_old_status AND v_new_status != 'error' THEN
        UPDATE driver_shifts
        SET status = v_new_status::shift_status,
            updated_at = p_reference_timestamp
        WHERE id = p_shift_id;

        -- Log the transition for monitoring
        RAISE NOTICE 'Shift status updated: ID=%, Truck=%, Driver=%, %->%',
            p_shift_id, v_truck_id, v_driver_id, v_old_status, v_new_status;

        RETURN QUERY SELECT v_old_status, v_new_status, TRUE, v_truck_id, v_driver_id;
    ELSE
        RETURN QUERY SELECT v_old_status, v_old_status, FALSE, v_truck_id, v_driver_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function for real-time shift status monitoring and validation
CREATE OR REPLACE FUNCTION get_shift_status_summary(
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
RETURNS TABLE (
    total_shifts INTEGER,
    active_shifts INTEGER,
    scheduled_shifts INTEGER,
    completed_shifts INTEGER,
    cancelled_shifts INTEGER,
    needs_activation INTEGER,
    needs_completion INTEGER,
    overnight_active INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH shift_analysis AS (
        SELECT
            ds.id,
            ds.status as current_status,
            evaluate_shift_status(ds.id, p_reference_timestamp) as calculated_status,
            CASE WHEN ds.end_time < ds.start_time THEN 1 ELSE 0 END as is_overnight
        FROM driver_shifts ds
        WHERE ds.status != 'cancelled'
    )
    SELECT
        COUNT(*)::INTEGER as total_shifts,
        COUNT(CASE WHEN current_status = 'active' THEN 1 END)::INTEGER as active_shifts,
        COUNT(CASE WHEN current_status = 'scheduled' THEN 1 END)::INTEGER as scheduled_shifts,
        COUNT(CASE WHEN current_status = 'completed' THEN 1 END)::INTEGER as completed_shifts,
        COUNT(CASE WHEN current_status = 'cancelled' THEN 1 END)::INTEGER as cancelled_shifts,
        COUNT(CASE WHEN current_status = 'scheduled' AND calculated_status = 'active' THEN 1 END)::INTEGER as needs_activation,
        COUNT(CASE WHEN current_status = 'active' AND calculated_status = 'completed' THEN 1 END)::INTEGER as needs_completion,
        COUNT(CASE WHEN current_status = 'active' AND is_overnight = 1 THEN 1 END)::INTEGER as overnight_active
    FROM shift_analysis;
END;
$$ LANGUAGE plpgsql;

-- Create function to validate shift time logic for testing
CREATE OR REPLACE FUNCTION test_shift_time_logic(
    p_start_time TIME,
    p_end_time TIME,
    p_test_time TIME,
    p_is_overnight BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
    is_overnight BOOLEAN,
    is_within_window BOOLEAN,
    logic_used TEXT
) AS $$
DECLARE
    v_is_overnight BOOLEAN;
    v_is_within_window BOOLEAN;
    v_logic_used TEXT;
BEGIN
    -- Determine if overnight (or use provided value for testing)
    v_is_overnight := COALESCE(p_is_overnight, p_end_time < p_start_time);

    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic
        v_is_within_window := (p_test_time >= p_start_time OR p_test_time <= p_end_time);
        v_logic_used := 'dual_condition_overnight';
    ELSE
        -- Day shift: Use simple BETWEEN logic
        v_is_within_window := (p_test_time BETWEEN p_start_time AND p_end_time);
        v_logic_used := 'simple_between_day';
    END IF;

    RETURN QUERY SELECT v_is_overnight, v_is_within_window, v_logic_used;
END;
$$ LANGUAGE plpgsql;

-- Create index for performance optimization
CREATE INDEX IF NOT EXISTS idx_driver_shifts_status_date
ON driver_shifts(status, start_date, end_date);

-- Create index for time-based queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_time_range
ON driver_shifts(start_time, end_time);

-- Create index for truck-based queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_truck_status
ON driver_shifts(truck_id, status) WHERE status IN ('active', 'scheduled');

-- Create index for recurrence pattern queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_recurrence_status
ON driver_shifts(recurrence_pattern, status, start_date, end_date);

-- Comment on the functions
COMMENT ON FUNCTION evaluate_shift_status(INTEGER, TIMESTAMP) IS
    'Evaluates the correct status for a shift based on enhanced date/time rules with proper overnight logic';

COMMENT ON FUNCTION update_all_shift_statuses(TIMESTAMP) IS
    'Updates status for all shifts based on current date/time with detailed statistics';

COMMENT ON FUNCTION update_shift_status(INTEGER, TIMESTAMP) IS
    'Updates status for a specific shift with enhanced validation and logging';

COMMENT ON FUNCTION get_shift_status_summary(TIMESTAMP) IS
    'Provides real-time summary of shift statuses for monitoring and validation';

COMMENT ON FUNCTION test_shift_time_logic(TIME, TIME, TIME, BOOLEAN) IS
    'Testing function to validate shift time logic for day and night shifts';