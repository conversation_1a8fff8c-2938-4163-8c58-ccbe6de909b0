#!/usr/bin/env node

/**
 * Automated Test Suite for Shift Management System
 * 
 * This script provides comprehensive testing for the shift management system,
 * including day/night shift logic, status transitions, and assignment display.
 * 
 * Run with: node scripts/automated-shift-tests.js
 */

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

// Test result tracking
let passedTests = 0;
let failedTests = 0;
let skippedTests = 0;

/**
 * Run a test and track results
 */
async function runTest(name, testFn) {
    try {
        console.log(`\n🧪 TEST: ${name}`);
        console.log(`   ${'='.repeat(50)}`);
        const result = await testFn();
        if (result === true) {
            console.log(`   ✅ PASSED: ${name}`);
            passedTests++;
        } else if (result === false) {
            console.log(`   ❌ FAILED: ${name}`);
            failedTests++;
        } else {
            console.log(`   ⚠️ SKIPPED: ${name}`);
            skippedTests++;
        }
    } catch (error) {
        console.error(`   ❌ ERROR: ${name} - ${error.message}`);
        failedTests++;
    }
}

/**
 * Test 1: Verify database functions exist with correct signatures
 */
async function testDatabaseFunctions() {
    const functions = await pool.query(`
        SELECT proname, pg_get_function_arguments(oid) as args
        FROM pg_proc 
        WHERE proname IN ('evaluate_shift_status', 'schedule_auto_activation', 'update_all_shift_statuses')
        ORDER BY proname
    `);
    
    if (functions.rows.length < 3) {
        console.log(`   ⚠️ Expected 3 functions, found ${functions.rows.length}`);
        return false;
    }
    
    // Check each function
    let allCorrect = true;
    functions.rows.forEach(func => {
        console.log(`   📋 ${func.proname}(${func.args})`);
        
        // Check evaluate_shift_status has 2 parameters
        if (func.proname === 'evaluate_shift_status' && 
            !func.args.includes('p_shift_id integer') && 
            !func.args.includes('p_reference_timestamp timestamp')) {
            console.log(`   ⚠️ evaluate_shift_status has incorrect signature`);
            allCorrect = false;
        }
        
        // Check schedule_auto_activation has no parameters
        if (func.proname === 'schedule_auto_activation' && func.args !== '') {
            console.log(`   ⚠️ schedule_auto_activation should have no parameters`);
            allCorrect = false;
        }
    });
    
    return allCorrect;
}

/**
 * Test 2: Verify shift status logic for day and night shifts
 */
async function testShiftStatusLogic() {
    // Create test shifts
    const testShifts = await pool.query(`
        WITH day_shift AS (
            INSERT INTO driver_shifts (
                truck_id, driver_id, shift_type, 
                start_date, end_date, 
                start_time, end_time, 
                status, recurrence_pattern
            )
            VALUES (
                1, 1, 'day', 
                CURRENT_DATE, CURRENT_DATE, 
                '06:00:00', '18:00:00', 
                'scheduled', 'single'
            )
            RETURNING id
        ),
        night_shift AS (
            INSERT INTO driver_shifts (
                truck_id, driver_id, shift_type, 
                start_date, end_date, 
                start_time, end_time, 
                status, recurrence_pattern
            )
            VALUES (
                1, 1, 'night', 
                CURRENT_DATE, CURRENT_DATE, 
                '18:00:00', '06:00:00', 
                'scheduled', 'single'
            )
            RETURNING id
        )
        SELECT 
            (SELECT id FROM day_shift) as day_shift_id,
            (SELECT id FROM night_shift) as night_shift_id
    `);
    
    if (!testShifts.rows[0]) {
        console.log('   ⚠️ Failed to create test shifts');
        return false;
    }
    
    const dayShiftId = testShifts.rows[0].day_shift_id;
    const nightShiftId = testShifts.rows[0].night_shift_id;
    
    console.log(`   📋 Created test day shift ID: ${dayShiftId}`);
    console.log(`   📋 Created test night shift ID: ${nightShiftId}`);
    
    // Get current time context
    const timeContext = await pool.query(`
        SELECT 
            CURRENT_DATE as current_date,
            CURRENT_TIME as current_time,
            EXTRACT(hour FROM CURRENT_TIME) as current_hour
    `);
    
    const ctx = timeContext.rows[0];
    console.log(`   📋 Current time: ${ctx.current_time.substring(0,8)} (Hour: ${ctx.current_hour})`);
    
    // Expected statuses based on current time
    const expectedDayStatus = ctx.current_hour >= 6 && ctx.current_hour < 18 ? 'active' : 'scheduled';
    const expectedNightStatus = ctx.current_hour >= 18 || ctx.current_hour < 6 ? 'active' : 'scheduled';
    
    console.log(`   📋 Expected day shift status: ${expectedDayStatus}`);
    console.log(`   📋 Expected night shift status: ${expectedNightStatus}`);
    
    // Run auto-activation to update statuses
    await pool.query('SELECT schedule_auto_activation()');
    
    // Check updated statuses
    const updatedShifts = await pool.query(`
        SELECT 
            id, 
            shift_type, 
            status, 
            evaluate_shift_status(id, CURRENT_TIMESTAMP) as calculated_status
        FROM driver_shifts 
        WHERE id IN ($1, $2)
    `, [dayShiftId, nightShiftId]);
    
    let allCorrect = true;
    updatedShifts.rows.forEach(shift => {
        const isDay = shift.shift_type === 'day';
        const expected = isDay ? expectedDayStatus : expectedNightStatus;
        
        console.log(`   📋 ${isDay ? 'Day' : 'Night'} shift status: ${shift.status} (Expected: ${expected})`);
        
        if (shift.status !== expected) {
            console.log(`   ⚠️ ${isDay ? 'Day' : 'Night'} shift has incorrect status`);
            allCorrect = false;
        }
        
        if (shift.status !== shift.calculated_status) {
            console.log(`   ⚠️ Status (${shift.status}) doesn't match calculated status (${shift.calculated_status})`);
            allCorrect = false;
        }
    });
    
    // Clean up test shifts
    await pool.query(`DELETE FROM driver_shifts WHERE id IN ($1, $2)`, [dayShiftId, nightShiftId]);
    console.log(`   🧹 Cleaned up test shifts`);
    
    return allCorrect;
}

/**
 * Test 3: Verify assignment display logic
 */
async function testAssignmentDisplay() {
    // Test assignment display logic using existing data
    const assignmentDisplay = await pool.query(`
        SELECT
            t.truck_number,
            d.full_name as assigned_driver,
            ds.shift_type as active_shift_type,
            ds.status as shift_status,
            CASE
                WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
                    CONCAT('✅ ', ds.shift_type, ' Shift Active')
                WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
                    CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
                ELSE '⚠️ No Active Shift'
            END as display_status
        FROM assignments a
        JOIN dump_trucks t ON a.truck_id = t.id
        LEFT JOIN drivers d ON a.driver_id = d.id
        LEFT JOIN driver_shifts ds ON (
            ds.truck_id = a.truck_id
            AND ds.status = 'active'
            AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
            AND (
                (ds.end_time < ds.start_time AND
                 (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
                OR
                (ds.end_time >= ds.start_time AND
                 CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
            )
        )
        LIMIT 1
    `);
    
    if (assignmentDisplay.rows.length === 0) {
        console.log('   ⚠️ No assignment display data found');
        return false;
    }
    
    const display = assignmentDisplay.rows[0];
    console.log(`   📋 Assignment display: ${display.display_status}`);
    console.log(`   📋 Truck: ${display.truck_number}, Driver: ${display.assigned_driver}`);
    
    // Check if display logic is working (should show some status)
    const isCorrect = display.display_status && 
                     (display.display_status.includes('✅') || 
                      display.display_status.includes('📅') || 
                      display.display_status.includes('⚠️'));
    
    if (!isCorrect) {
        console.log(`   ⚠️ Assignment display logic not working correctly`);
    }
    
    return isCorrect;
}

/**
 * Test 4: Verify overnight shift logic
 */
async function testOvernightShiftLogic() {
    // Create test overnight shift spanning multiple dates
    const testShift = await pool.query(`
        INSERT INTO driver_shifts (
            truck_id, driver_id, shift_type, 
            start_date, end_date, 
            start_time, end_time, 
            status, recurrence_pattern
        )
        VALUES (
            1, 1, 'night', 
            CURRENT_DATE - INTERVAL '1 day', CURRENT_DATE + INTERVAL '1 day', 
            '22:00:00', '06:00:00', 
            'scheduled', 'single'
        )
        RETURNING id
    `);
    
    if (!testShift.rows[0]) {
        console.log('   ⚠️ Failed to create test overnight shift');
        return false;
    }
    
    const shiftId = testShift.rows[0].id;
    console.log(`   📋 Created test overnight shift ID: ${shiftId}`);
    
    // Test different times
    const testTimes = [
        { time: '21:00:00', expected: 'scheduled', label: 'Before start (9 PM)' },
        { time: '23:00:00', expected: 'active', label: 'During evening (11 PM)' },
        { time: '03:00:00', expected: 'active', label: 'During morning (3 AM)' },
        { time: '07:00:00', expected: 'scheduled', label: 'After end (7 AM)' }
    ];
    
    let allCorrect = true;
    
    for (const test of testTimes) {
        // Test with specific timestamp
        const result = await pool.query(`
            SELECT evaluate_shift_status($1, CURRENT_DATE + $2::TIME) as status
        `, [shiftId, test.time]);
        
        const status = result.rows[0].status;
        console.log(`   📋 ${test.label}: ${status} (Expected: ${test.expected})`);
        
        if (status !== test.expected) {
            console.log(`   ⚠️ Incorrect status for ${test.label}`);
            allCorrect = false;
        }
    }
    
    // Clean up test shift
    await pool.query(`DELETE FROM driver_shifts WHERE id = $1`, [shiftId]);
    console.log(`   🧹 Cleaned up test overnight shift`);
    
    return allCorrect;
}

/**
 * Test 5: Verify schedule_auto_activation function
 */
async function testAutoActivation() {
    // Create test shifts with specific statuses
    const testShifts = await pool.query(`
        WITH active_shift AS (
            INSERT INTO driver_shifts (
                truck_id, driver_id, shift_type, 
                start_date, end_date, 
                start_time, end_time, 
                status, recurrence_pattern
            )
            VALUES (
                1, 1, 'day', 
                CURRENT_DATE - INTERVAL '1 day', CURRENT_DATE - INTERVAL '1 day', 
                '06:00:00', '18:00:00', 
                'active', 'single'
            )
            RETURNING id
        ),
        scheduled_shift AS (
            INSERT INTO driver_shifts (
                truck_id, driver_id, shift_type, 
                start_date, end_date, 
                start_time, end_time, 
                status, recurrence_pattern
            )
            VALUES (
                1, 1, 'day', 
                CURRENT_DATE, CURRENT_DATE, 
                '00:00:00', '23:59:59', 
                'scheduled', 'single'
            )
            RETURNING id
        )
        SELECT 
            (SELECT id FROM active_shift) as active_shift_id,
            (SELECT id FROM scheduled_shift) as scheduled_shift_id
    `);
    
    if (!testShifts.rows[0]) {
        console.log('   ⚠️ Failed to create test shifts');
        return false;
    }
    
    const activeShiftId = testShifts.rows[0].active_shift_id;
    const scheduledShiftId = testShifts.rows[0].scheduled_shift_id;
    
    console.log(`   📋 Created test active shift ID: ${activeShiftId}`);
    console.log(`   📋 Created test scheduled shift ID: ${scheduledShiftId}`);
    
    // Check initial statuses
    const initialStatus = await pool.query(`
        SELECT id, status FROM driver_shifts WHERE id IN ($1, $2)
    `, [activeShiftId, scheduledShiftId]);
    
    initialStatus.rows.forEach(shift => {
        console.log(`   📋 Initial status for shift ${shift.id}: ${shift.status}`);
    });
    
    // Run auto-activation
    await pool.query('SELECT schedule_auto_activation()');
    console.log(`   📋 Ran schedule_auto_activation()`);
    
    // Check updated statuses
    const updatedStatus = await pool.query(`
        SELECT id, status FROM driver_shifts WHERE id IN ($1, $2)
    `, [activeShiftId, scheduledShiftId]);
    
    let allCorrect = true;
    updatedStatus.rows.forEach(shift => {
        console.log(`   📋 Updated status for shift ${shift.id}: ${shift.status}`);
        
        // Past shift should be completed
        if (shift.id === activeShiftId && shift.status !== 'completed') {
            console.log(`   ⚠️ Past active shift should be completed`);
            allCorrect = false;
        }
        
        // Current all-day shift should be active
        if (shift.id === scheduledShiftId && shift.status !== 'active') {
            console.log(`   ⚠️ Current scheduled shift should be active`);
            allCorrect = false;
        }
    });
    
    // Clean up test shifts
    await pool.query(`DELETE FROM driver_shifts WHERE id IN ($1, $2)`, [activeShiftId, scheduledShiftId]);
    console.log(`   🧹 Cleaned up test shifts`);
    
    return allCorrect;
}

/**
 * Run all tests
 */
async function runAllTests() {
    console.log('🚀 Starting Automated Shift Management Tests...\n');
    
    try {
        await runTest('Database Functions', testDatabaseFunctions);
        await runTest('Shift Status Logic', testShiftStatusLogic);
        await runTest('Assignment Display Logic', testAssignmentDisplay);
        await runTest('Overnight Shift Logic', testOvernightShiftLogic);
        await runTest('Auto-Activation Function', testAutoActivation);
        
        // Print summary
        console.log('\n📊 TEST SUMMARY:');
        console.log(`   ✅ Passed: ${passedTests}`);
        console.log(`   ❌ Failed: ${failedTests}`);
        console.log(`   ⚠️ Skipped: ${skippedTests}`);
        console.log(`   📋 Total: ${passedTests + failedTests + skippedTests}`);
        
        if (failedTests === 0) {
            console.log('\n🎉 ALL TESTS PASSED! Shift management system is working correctly.');
        } else {
            console.log('\n⚠️ SOME TESTS FAILED. Please review the issues above.');
        }
    } catch (error) {
        console.error('❌ Test suite error:', error.message);
    } finally {
        await pool.end();
    }
}

// Run tests if executed directly
if (require.main === module) {
    runAllTests();
}

module.exports = { runAllTests };