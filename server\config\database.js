const { Pool } = require('pg');
require('dotenv').config();

// Enhanced database configuration with monitoring and optimization
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: String(process.env.DB_PASSWORD || 'PostgreSQLPassword'),
  
  // Enhanced connection pool configuration
  max: parseInt(process.env.DB_POOL_MAX) || 25, // Maximum connections
  min: parseInt(process.env.DB_POOL_MIN) || 5,  // Minimum connections
  acquireTimeoutMillis: 30000, // 30 seconds to acquire connection
  createTimeoutMillis: 30000,  // 30 seconds to create connection
  destroyTimeoutMillis: 5000,  // 5 seconds to destroy connection
  idleTimeoutMillis: 60000,    // 60 seconds idle timeout
  reapIntervalMillis: 1000,    // Check for idle connections every second
  createRetryIntervalMillis: 200, // Retry connection creation every 200ms
  
  // Enhanced configuration options
  statement_timeout: 30000,    // 30 second statement timeout
  query_timeout: 25000,        // 25 second query timeout
  application_name: 'hauling-qr-system',
  
  // SSL configuration for production
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false
  } : false
};

// Create enhanced connection pool
const pool = new Pool(dbConfig);

// Connection pool monitoring
let connectionMetrics = {
  totalConnections: 0,
  activeConnections: 0,
  idleConnections: 0,
  waitingClients: 0,
  totalQueries: 0,
  totalErrors: 0,
  avgQueryTime: 0,
  slowQueries: []
};

// Pool event handlers for monitoring
pool.on('connect', (client) => {
  connectionMetrics.totalConnections++;
  // Database connection established

  // Add query monitoring to each connection (only for slow queries)
  const originalQuery = client.query;
  client.query = function(...args) {
    const start = Date.now();
    const result = originalQuery.apply(client, args);

    if (result && typeof result.then === 'function') {
      return result.then((res) => {
        const duration = Date.now() - start;
        if (duration > 1000) { // Only log slow queries
          logQueryMetrics(args[0], duration, null);
        }
        return res;
      }).catch((err) => {
        const duration = Date.now() - start;
        logQueryMetrics(args[0], duration, err);
        throw err;
      });
    }

    return result;
  };
});

pool.on('acquire', () => {
  connectionMetrics.activeConnections++;
});

pool.on('release', () => {
  connectionMetrics.activeConnections--;
  connectionMetrics.idleConnections++;
});

pool.on('error', (err, client) => {
  connectionMetrics.totalErrors++;
  console.error('❌ Database pool error:', err.message);
  
  // Attempt to reconnect on connection errors
  if (err.code === 'ECONNRESET' || err.code === 'ENOTFOUND') {
    console.log('🔄 Attempting to reconnect to database...');
    setTimeout(() => testConnection(), 5000);
  }
});

// Query metrics logging
function logQueryMetrics(queryText, duration, error) {
  connectionMetrics.totalQueries++;
  
  // Update average query time
  connectionMetrics.avgQueryTime = (
    (connectionMetrics.avgQueryTime * (connectionMetrics.totalQueries - 1) + duration) /
    connectionMetrics.totalQueries
  );
  
  // Track slow queries (> 1000ms)
  if (duration > 1000) {
    const slowQuery = {
      query: typeof queryText === 'string' ? queryText.substring(0, 100) : 'Unknown',
      duration,
      timestamp: new Date().toISOString(),
      error: error ? error.message : null
    };
    
    connectionMetrics.slowQueries.push(slowQuery);
    
    // Keep only last 50 slow queries
    if (connectionMetrics.slowQueries.length > 50) {
      connectionMetrics.slowQueries.shift();
    }
    
    console.warn(`Slow query detected (${duration}ms):`, slowQuery.query);
  }
  
  if (error) {
    connectionMetrics.totalErrors++;
    console.error(`Query error (${duration}ms):`, error.message);
  }
  // Only log slow queries to reduce console noise
}

// Enhanced query function with retry logic and monitoring
const query = async (text, params, retries = 3) => {
  const queryId = Math.random().toString(36).substr(2, 9);
  const start = Date.now();
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`🔍 [${queryId}] Executing query (attempt ${attempt}/${retries})`);
      
      const res = await pool.query(text, params);
      const duration = Date.now() - start;
      
      console.log(`✅ [${queryId}] Query completed in ${duration}ms (${res.rowCount} rows)`);
      
      return res;
    } catch (error) {
      const duration = Date.now() - start;
      
      // Retry on connection errors
      if (attempt < retries && (
        error.code === 'ECONNRESET' ||
        error.code === 'ENOTFOUND' ||
        error.code === '57P01' || // admin_shutdown
        error.code === '57P02' || // crash_shutdown
        error.code === '57P03'    // cannot_connect_now
      )) {
        console.warn(`⚠️ [${queryId}] Connection error on attempt ${attempt}, retrying...`, error.message);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
        continue;
      }
      
      console.error(`❌ [${queryId}] Query failed after ${duration}ms:`, error.message);
      throw error;
    }
  }
  
  throw new Error(`Query failed after ${retries} attempts`);
};

// Enhanced transaction management
const transaction = async (callback, retries = 2) => {
  const client = await getClient();
  const transactionId = Math.random().toString(36).substr(2, 9);
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`🔄 [${transactionId}] Starting transaction (attempt ${attempt}/${retries})`);
      
      await client.query('BEGIN');
      
      const result = await callback(client);
      
      await client.query('COMMIT');
      console.log(`✅ [${transactionId}] Transaction committed successfully`);
      
      return result;
    } catch (error) {
      try {
        await client.query('ROLLBACK');
        console.log(`↩️ [${transactionId}] Transaction rolled back`);
      } catch (rollbackError) {
        console.error(`❌ [${transactionId}] Rollback failed:`, rollbackError.message);
      }
      
      // Retry on serialization or deadlock errors
      if (attempt < retries && (
        error.code === '40001' || // serialization_failure
        error.code === '40P01'    // deadlock_detected
      )) {
        console.warn(`⚠️ [${transactionId}] Transaction conflict on attempt ${attempt}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, 100 * attempt));
        continue;
      }
      
      console.error(`❌ [${transactionId}] Transaction failed:`, error.message);
      throw error;
    } finally {
      client.release();
    }
  }
};

// Enhanced client acquisition with timeout
const getClient = async (timeout = 10000) => {
  const start = Date.now();
  
  try {
    const client = await pool.connect();
    const duration = Date.now() - start;
    
    console.log(`🤝 Client acquired in ${duration}ms`);
    return client;
  } catch (error) {
    const duration = Date.now() - start;
    console.error(`❌ Failed to acquire client after ${duration}ms:`, error.message);
    throw error;
  }
};

// Database health check with comprehensive metrics
const healthCheck = async () => {
  try {
    const start = Date.now();
    
    // Basic connectivity test
    const connectResult = await query('SELECT NOW() as current_time, version() as db_version');
    const connectDuration = Date.now() - start;
    
    // Pool status
    const poolStatus = {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    };
    
    // Performance metrics
    const performanceTest = await query(`
      SELECT
        COUNT(*) as total_tables,
        (SELECT COUNT(*) FROM trip_logs WHERE created_at >= CURRENT_DATE) as trips_today,
        (SELECT COUNT(*) FROM approvals WHERE status = 'pending') as pending_approvals
    `);
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connection: {
        response_time_ms: connectDuration,
        database_time: connectResult.rows[0].current_time,
        database_version: connectResult.rows[0].db_version
      },
      pool: poolStatus,
      metrics: connectionMetrics,
      performance: performanceTest.rows[0]
    };
    
    console.log('💚 Database health check passed');
    return healthData;
  } catch (error) {
    console.error('💔 Database health check failed:', error.message);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      pool: {
        totalCount: pool.totalCount,
        idleCount: pool.idleCount,
        waitingCount: pool.waitingCount
      }
    };
  }
};

// Connection testing with retry logic
const testConnection = async (retries = 5) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const result = await query('SELECT NOW() as current_time, current_database() as db_name');
      
      console.log(`✅ Database connection successful (attempt ${attempt}/${retries}):`, {
        time: result.rows[0].current_time,
        database: result.rows[0].db_name,
        pool_total: pool.totalCount,
        pool_idle: pool.idleCount
      });
      
      return true;
    } catch (error) {
      console.error(`❌ Database connection failed (attempt ${attempt}/${retries}):`, error.message);
      
      if (attempt < retries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Exponential backoff, max 10s
        console.log(`⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.error('💔 Database connection failed after all retry attempts');
  return false;
};

// Graceful shutdown
const gracefulShutdown = async () => {
  console.log('🔄 Initiating graceful database shutdown...');
  
  try {
    await pool.end();
    console.log('✅ Database pool closed successfully');
  } catch (error) {
    console.error('❌ Error during database shutdown:', error.message);
  }
};

// Performance monitoring report
const getPerformanceReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    connections: {
      total: connectionMetrics.totalConnections,
      active: connectionMetrics.activeConnections,
      idle: connectionMetrics.idleConnections,
      waiting: connectionMetrics.waitingClients,
      pool_total: pool.totalCount,
      pool_idle: pool.idleCount,
      pool_waiting: pool.waitingCount
    },
    queries: {
      total: connectionMetrics.totalQueries,
      errors: connectionMetrics.totalErrors,
      avg_duration_ms: Math.round(connectionMetrics.avgQueryTime),
      slow_queries_count: connectionMetrics.slowQueries.length,
      error_rate: connectionMetrics.totalQueries > 0 ?
        ((connectionMetrics.totalErrors / connectionMetrics.totalQueries) * 100).toFixed(2) : 0
    },
    slow_queries: connectionMetrics.slowQueries.slice(-10) // Last 10 slow queries
  };
  
  return report;
};

// Legacy function aliases for backward compatibility
const beginTransaction = async () => {
  const client = await getClient();
  await client.query('BEGIN');
  return client;
};

const commitTransaction = async (client) => {
  await client.query('COMMIT');
  client.release();
};

const rollbackTransaction = async (client) => {
  await client.query('ROLLBACK');
  client.release();
};

// Periodic health monitoring (every 5 minutes)
setInterval(async () => {
  const health = await healthCheck();
  if (health.status === 'unhealthy') {
    console.error('🚨 Database health check failed - investigate immediately');
  }
}, 5 * 60 * 1000);

// Process exit handlers
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);
process.on('exit', gracefulShutdown);

module.exports = {
  pool,
  query,
  getClient,
  transaction,
  healthCheck,
  testConnection,
  gracefulShutdown,
  getPerformanceReport,
  connectionMetrics,
  // Legacy aliases for backward compatibility
  beginTransaction,
  commitTransaction,
  rollbackTransaction
};