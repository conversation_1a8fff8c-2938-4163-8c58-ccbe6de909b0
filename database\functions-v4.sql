-- ============================================================================
-- Additional Functions for Database v4.0 Schema Completion
-- These functions are extracted from the current database to match exactly
-- ============================================================================

-- Function to add sample shift data
CREATE OR REPLACE FUNCTION add_sample_shift_data() RETURNS INTEGER
LANGUAGE plpgsql AS $$
DECLARE
    v_inserted_count INTEGER := 0;
BEGIN
    -- Insert sample shift data if not exists
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    SELECT 1, 1, 'day', CURRENT_DATE, CURRENT_DATE + INTERVAL '7 days', '06:00', '18:00', 'scheduled', 'daily'
    WHERE NOT EXISTS (SELECT 1 FROM driver_shifts WHERE truck_id = 1 AND driver_id = 1);
    
    GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
    RETURN v_inserted_count;
END;
$$;

-- Enhanced auto-activate shifts function
CREATE OR REPLACE FUNCTION auto_activate_shifts_enhanced() RETURNS INTEGER
LANGUAGE plpgsql AS $$
DECLARE
    v_activated_count INTEGER := 0;
    v_shift_record RECORD;
    v_calculated_status TEXT;
BEGIN
    FOR v_shift_record IN 
        SELECT id, status FROM driver_shifts 
        WHERE status = 'scheduled'
    LOOP
        v_calculated_status := evaluate_shift_status(v_shift_record.id, CURRENT_TIMESTAMP);
        
        IF v_calculated_status = 'active' THEN
            UPDATE driver_shifts 
            SET status = 'active', updated_at = CURRENT_TIMESTAMP 
            WHERE id = v_shift_record.id;
            v_activated_count := v_activated_count + 1;
        END IF;
    END LOOP;
    
    RETURN v_activated_count;
END;
$$;

-- Auto-capture trip driver trigger function
CREATE OR REPLACE FUNCTION auto_capture_trip_driver() RETURNS TRIGGER
LANGUAGE plpgsql AS $$
DECLARE
    driver_info RECORD;
    truck_id INTEGER;
BEGIN
    -- Get truck_id from assignment
    SELECT a.truck_id INTO truck_id
    FROM assignments a
    WHERE a.id = NEW.assignment_id;
    
    -- Capture active driver information
    SELECT * INTO driver_info
    FROM capture_active_driver_for_trip_enhanced(truck_id, CURRENT_TIMESTAMP);
    
    -- Update trip with driver information
    NEW.performed_by_driver_id := driver_info.driver_id;
    NEW.performed_by_driver_name := driver_info.driver_name;
    NEW.performed_by_employee_id := driver_info.employee_id;
    NEW.performed_by_shift_id := driver_info.shift_id;
    NEW.performed_by_shift_type := driver_info.shift_type;
    
    RETURN NEW;
END;
$$;

-- Auto-complete shifts enhanced function
CREATE OR REPLACE FUNCTION auto_complete_shifts_enhanced() RETURNS INTEGER
LANGUAGE plpgsql AS $$
DECLARE
    v_completed_count INTEGER := 0;
BEGIN
    UPDATE driver_shifts 
    SET status = 'completed', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active' 
        AND end_date < CURRENT_DATE
        AND CURRENT_TIME > end_time;
    
    GET DIAGNOSTICS v_completed_count = ROW_COUNT;
    RETURN v_completed_count;
END;
$$;

-- Auto-populate driver from shift trigger function
CREATE OR REPLACE FUNCTION auto_populate_driver_from_shift() RETURNS TRIGGER
LANGUAGE plpgsql AS $$
DECLARE
    v_current_driver_id INTEGER;
BEGIN
    -- If driver_id is null, try to get from active shift
    IF NEW.driver_id IS NULL THEN
        SELECT driver_id INTO v_current_driver_id
        FROM get_current_driver_for_truck_enhanced(NEW.truck_id);
        
        IF v_current_driver_id IS NOT NULL THEN
            NEW.driver_id := v_current_driver_id;
            NEW.is_shift_assignment := true;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Calculate shift end timestamp function
CREATE OR REPLACE FUNCTION calculate_shift_end_timestamp(
    end_date DATE, 
    end_time TIME, 
    is_overnight BOOLEAN
) RETURNS TIMESTAMP
LANGUAGE plpgsql IMMUTABLE AS $$
BEGIN
    IF is_overnight THEN
        RETURN (end_date + INTERVAL '1 day')::DATE + end_time;
    ELSE
        RETURN end_date::TIMESTAMP + end_time;
    END IF;
END;
$$;

-- Calculate shift status function
CREATE OR REPLACE FUNCTION calculate_shift_status(
    within_date_range BOOLEAN, 
    within_time_window BOOLEAN
) RETURNS TEXT
LANGUAGE plpgsql IMMUTABLE AS $$
BEGIN
    CASE 
        WHEN within_date_range AND within_time_window THEN
            RETURN 'active';
        WHEN within_date_range THEN
            RETURN 'scheduled';
        ELSE
            RETURN 'scheduled';
    END CASE;
END;
$$;

-- Capture active driver for trip function
CREATE OR REPLACE FUNCTION capture_active_driver_for_trip(
    p_truck_id INTEGER, 
    p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE(
    driver_id INTEGER, 
    driver_name VARCHAR, 
    employee_id VARCHAR, 
    shift_id INTEGER, 
    shift_type shift_type
)
LANGUAGE plpgsql AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.id as shift_id,
        ds.shift_type
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
        AND ds.status = 'active'
        AND p_timestamp::DATE BETWEEN ds.start_date AND ds.end_date
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$;

-- Enhanced capture active driver for trip function
CREATE OR REPLACE FUNCTION capture_active_driver_for_trip_enhanced(
    p_truck_id INTEGER, 
    p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE(
    driver_id INTEGER, 
    driver_name VARCHAR, 
    employee_id VARCHAR, 
    shift_id INTEGER, 
    shift_type shift_type, 
    display_type shift_type
)
LANGUAGE plpgsql AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.id as shift_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
        AND ds.status = 'active'
        AND p_timestamp::DATE BETWEEN ds.start_date AND ds.end_date
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$;

-- Check shift status consistency function
CREATE OR REPLACE FUNCTION check_shift_status_consistency() 
RETURNS TABLE(
    shift_id INTEGER, 
    current_status shift_status, 
    expected_status TEXT, 
    truck_id INTEGER, 
    driver_id INTEGER, 
    shift_type shift_type, 
    start_date DATE, 
    end_date DATE, 
    start_time TIME, 
    end_time TIME, 
    issue_description TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    v_shift RECORD;
    v_expected_status TEXT;
BEGIN
    FOR v_shift IN 
        SELECT * FROM driver_shifts WHERE status != 'cancelled'
    LOOP
        v_expected_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
        
        IF v_expected_status != v_shift.status::TEXT THEN
            shift_id := v_shift.id;
            current_status := v_shift.status;
            expected_status := v_expected_status;
            truck_id := v_shift.truck_id;
            driver_id := v_shift.driver_id;
            shift_type := v_shift.shift_type;
            start_date := v_shift.start_date;
            end_date := v_shift.end_date;
            start_time := v_shift.start_time;
            end_time := v_shift.end_time;
            issue_description := 'Status mismatch: current=' || v_shift.status::TEXT || ', expected=' || v_expected_status;
            
            RETURN NEXT;
        END IF;
    END LOOP;
END;
$$;

-- Classify shift by time function
CREATE OR REPLACE FUNCTION classify_shift_by_time(
    p_start_time TIME, 
    p_end_time TIME
) RETURNS shift_type
LANGUAGE plpgsql AS $$
DECLARE
    v_start_minutes INTEGER;
    v_end_minutes INTEGER;
BEGIN
    v_start_minutes := EXTRACT(HOUR FROM p_start_time) * 60 + EXTRACT(MINUTE FROM p_start_time);
    v_end_minutes := EXTRACT(HOUR FROM p_end_time) * 60 + EXTRACT(MINUTE FROM p_end_time);
    
    -- Day shift: 6 AM to 6 PM
    IF v_start_minutes >= 360 AND v_end_minutes <= 1080 THEN
        RETURN 'day';
    -- Night shift: 6 PM to 6 AM (overnight)
    ELSIF v_start_minutes >= 1080 OR v_end_minutes <= 360 THEN
        RETURN 'night';
    ELSE
        RETURN 'custom';
    END IF;
END;
$$;

-- Get current active driver function
CREATE OR REPLACE FUNCTION get_current_active_driver(p_truck_id INTEGER) RETURNS INTEGER
LANGUAGE plpgsql AS $$
DECLARE
    v_driver_id INTEGER;
BEGIN
    SELECT driver_id INTO v_driver_id
    FROM driver_shifts
    WHERE truck_id = p_truck_id
        AND status = 'active'
        AND CURRENT_DATE BETWEEN start_date AND end_date
        AND CURRENT_TIME BETWEEN start_time AND
            CASE
                WHEN end_time < start_time
                THEN end_time + interval '24 hours'
                ELSE end_time
            END
    ORDER BY created_at DESC
    LIMIT 1;

    RETURN v_driver_id;
END;
$$;

-- Enhanced get current driver for truck function
CREATE OR REPLACE FUNCTION get_current_driver_for_truck_enhanced(
    p_truck_id INTEGER,
    p_check_date DATE DEFAULT CURRENT_DATE,
    p_check_time TIME DEFAULT CURRENT_TIME
) RETURNS TABLE(
    driver_id INTEGER,
    driver_name VARCHAR,
    employee_id VARCHAR,
    shift_type shift_type,
    display_type shift_type,
    shift_id INTEGER
)
LANGUAGE plpgsql AS $$
BEGIN
    RETURN QUERY
    SELECT
        d.id as driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.id as shift_id
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
        AND ds.status = 'active'
        AND p_check_date BETWEEN ds.start_date AND ds.end_date
        AND (
            (ds.start_time <= ds.end_time AND p_check_time BETWEEN ds.start_time AND ds.end_time) OR
            (ds.start_time > ds.end_time AND (p_check_time >= ds.start_time OR p_check_time <= ds.end_time))
        )
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$;

-- Get active shifts for date function
CREATE OR REPLACE FUNCTION get_active_shifts_for_date(p_check_date DATE DEFAULT CURRENT_DATE)
RETURNS TABLE(
    shift_id INTEGER,
    truck_id INTEGER,
    driver_id INTEGER,
    shift_type shift_type,
    display_type shift_type,
    recurrence_pattern recurrence_pattern,
    start_time TIME,
    end_time TIME,
    status shift_status
)
LANGUAGE plpgsql AS $$
BEGIN
    RETURN QUERY
    SELECT
        ds.id as shift_id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.recurrence_pattern,
        ds.start_time,
        ds.end_time,
        ds.status
    FROM driver_shifts ds
    WHERE ds.status = 'active'
        AND p_check_date BETWEEN ds.start_date AND ds.end_date
    ORDER BY ds.truck_id, ds.start_time;
END;
$$;

-- Get shift display date function
CREATE OR REPLACE FUNCTION get_shift_display_date(
    p_shift_date DATE,
    p_start_date DATE,
    p_end_date DATE,
    p_recurrence_pattern TEXT
) RETURNS TEXT
LANGUAGE plpgsql AS $$
BEGIN
    -- Unified approach: always use start_date/end_date
    IF p_start_date = p_end_date THEN
        RETURN p_start_date::TEXT;
    ELSE
        RETURN p_start_date::TEXT || ' to ' || p_end_date::TEXT;
    END IF;
END;
$$;

-- Get shift status summary function
CREATE OR REPLACE FUNCTION get_shift_status_summary(
    p_reference_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE(
    total_shifts INTEGER,
    active_shifts INTEGER,
    scheduled_shifts INTEGER,
    completed_shifts INTEGER,
    cancelled_shifts INTEGER,
    needs_activation INTEGER,
    needs_completion INTEGER,
    overnight_active INTEGER
)
LANGUAGE plpgsql AS $$
BEGIN
    RETURN QUERY
    WITH shift_analysis AS (
        SELECT
            ds.*,
            evaluate_shift_status(ds.id, p_reference_timestamp) as calculated_status,
            (ds.end_time < ds.start_time) as is_overnight
        FROM driver_shifts ds
    )
    SELECT
        COUNT(*)::INTEGER as total_shifts,
        COUNT(CASE WHEN status = 'active' THEN 1 END)::INTEGER as active_shifts,
        COUNT(CASE WHEN status = 'scheduled' THEN 1 END)::INTEGER as scheduled_shifts,
        COUNT(CASE WHEN status = 'completed' THEN 1 END)::INTEGER as completed_shifts,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END)::INTEGER as cancelled_shifts,
        COUNT(CASE WHEN status != 'active' AND calculated_status = 'active' THEN 1 END)::INTEGER as needs_activation,
        COUNT(CASE WHEN status = 'active' AND calculated_status != 'active' THEN 1 END)::INTEGER as needs_completion,
        COUNT(CASE WHEN status = 'active' AND is_overnight THEN 1 END)::INTEGER as overnight_active
    FROM shift_analysis;
END;
$$;

-- Is overnight shift function
CREATE OR REPLACE FUNCTION is_overnight_shift(start_time TIME, end_time TIME) RETURNS BOOLEAN
LANGUAGE plpgsql IMMUTABLE AS $$
BEGIN
    RETURN end_time < start_time;
END;
$$;

-- Is shift active on date function
CREATE OR REPLACE FUNCTION is_shift_active_on_date(
    p_shift_id INTEGER,
    p_check_date DATE DEFAULT CURRENT_DATE
) RETURNS BOOLEAN
LANGUAGE plpgsql AS $$
DECLARE
    v_shift RECORD;
    v_is_active BOOLEAN := FALSE;
BEGIN
    SELECT * INTO v_shift FROM driver_shifts WHERE id = p_shift_id;

    IF v_shift.id IS NULL THEN
        RETURN FALSE;
    END IF;

    -- Check if date is within shift range
    IF p_check_date BETWEEN v_shift.start_date AND v_shift.end_date THEN
        -- Check recurrence pattern
        CASE v_shift.recurrence_pattern
            WHEN 'single' THEN
                v_is_active := (p_check_date = v_shift.start_date);
            WHEN 'daily' THEN
                v_is_active := TRUE;
            WHEN 'weekly' THEN
                v_is_active := (EXTRACT(DOW FROM p_check_date) = EXTRACT(DOW FROM v_shift.start_date));
            WHEN 'weekdays' THEN
                v_is_active := (EXTRACT(DOW FROM p_check_date) BETWEEN 1 AND 5);
            WHEN 'weekends' THEN
                v_is_active := (EXTRACT(DOW FROM p_check_date) IN (0, 6));
            ELSE
                v_is_active := TRUE; -- custom pattern, assume active
        END CASE;
    END IF;

    RETURN v_is_active;
END;
$$;
