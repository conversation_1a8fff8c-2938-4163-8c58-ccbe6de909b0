-- ============================================================================
-- Migration: Restore shift_date as Optional Column
-- Purpose: Add shift_date back as optional for backward compatibility while maintaining unified approach
-- Date: 2025-07-14
-- ============================================================================

-- Add shift_date column back as optional
ALTER TABLE driver_shifts 
ADD COLUMN IF NOT EXISTS shift_date DATE;

-- Create auto-sync function to maintain shift_date = start_date
CREATE OR REPLACE FUNCTION sync_shift_date_with_start_date()
RETURNS TRIGGER AS $$
BEGIN
  -- Auto-sync shift_date with start_date for backward compatibility
  -- This ensures existing code that references shift_date continues to work
  NEW.shift_date = NEW.start_date;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-sync shift_date with start_date
DROP TRIGGER IF EXISTS trigger_sync_shift_date ON driver_shifts;
CREATE TRIGGER trigger_sync_shift_date
  BEFORE INSERT OR UPDATE ON driver_shifts
  FOR EACH ROW
  EXECUTE FUNCTION sync_shift_date_with_start_date();

-- Update existing records to have shift_date = start_date
UPDATE driver_shifts 
SET shift_date = start_date 
WHERE shift_date IS NULL AND start_date IS NOT NULL;

-- Add comment explaining the approach
COMMENT ON COLUMN driver_shifts.shift_date IS 
'Optional backward compatibility column. Auto-synced with start_date via trigger. Maintained for legacy code compatibility while unified approach uses start_date/end_date.';

-- Add index for shift_date queries (for performance)
CREATE INDEX IF NOT EXISTS idx_driver_shifts_shift_date 
ON driver_shifts (shift_date);

-- Create index for common shift_date + truck_id queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_truck_shift_date 
ON driver_shifts (truck_id, shift_date);

-- Test the implementation
DO $$
DECLARE
    test_count INTEGER;
    sync_test_count INTEGER;
BEGIN
    -- Count total shifts
    SELECT COUNT(*) INTO test_count FROM driver_shifts;
    
    -- Count shifts where shift_date = start_date (should be all of them)
    SELECT COUNT(*) INTO sync_test_count 
    FROM driver_shifts 
    WHERE shift_date = start_date OR (shift_date IS NULL AND start_date IS NULL);
    
    RAISE NOTICE 'Optional shift_date implementation validation:';
    RAISE NOTICE '- Total shifts: %', test_count;
    RAISE NOTICE '- Properly synced shifts: %', sync_test_count;
    
    IF test_count = sync_test_count THEN
        RAISE NOTICE '✅ All shifts properly synced';
    ELSE
        RAISE NOTICE '⚠️ Some shifts not properly synced';
    END IF;
END $$;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 041 completed successfully: shift_date restored as optional';
    RAISE NOTICE '- Added shift_date column as optional (nullable)';
    RAISE NOTICE '- Created auto-sync trigger with start_date';
    RAISE NOTICE '- Updated existing records to sync shift_date = start_date';
    RAISE NOTICE '- Added performance indexes for shift_date queries';
    RAISE NOTICE '- Maintained unified approach: start_date/end_date primary';
    RAISE NOTICE '- Backward compatibility: shift_date available for legacy code';
    RAISE NOTICE '- Auto-sync ensures consistency between shift_date and start_date';
END $$;
