-- Schema Validation for Database Init v4.0
-- Quick validation queries to ensure all objects were created correctly

-- Check table count (should be 13 tables)
SELECT COUNT(*) as table_count FROM information_schema.tables 
WHERE table_schema = 'public' AND table_type = 'BASE TABLE';

-- Check enum count (should be 10 enums)
SELECT COUNT(*) as enum_count FROM pg_type WHERE typtype = 'e';

-- Check function count
SELECT COUNT(*) as function_count FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';

-- Check view count
SELECT COUNT(*) as view_count FROM information_schema.views WHERE table_schema = 'public';

-- Check if exception_triggered exists in trip_status
SELECT EXISTS(
    SELECT 1 FROM pg_enum e 
    JOIN pg_type t ON e.enumtypid = t.oid 
    WHERE t.typname = 'trip_status' AND e.enumlabel = 'exception_triggered'
) as has_exception_triggered;

-- Check migration tracking
SELECT COUNT(*) as migrations_tracked FROM schema_migrations;

-- Check sample data
SELECT 
    (SELECT COUNT(*) FROM users) as users_count,
    (SELECT COUNT(*) FROM locations) as locations_count,
    (SELECT COUNT(*) FROM drivers) as drivers_count,
    (SELECT COUNT(*) FROM dump_trucks) as trucks_count;
