-- ============================================================================
-- SCHEMA ANALYSIS FOR MIGRATION CONSOLIDATION
-- Generated from production database pg_dump analysis
-- ============================================================================

-- This file contains analysis queries to understand the current schema state
-- and validate the consolidation process

-- ============================================================================
-- ENUM TYPES ANALYSIS (11 total)
-- ============================================================================

-- Query to verify all enum types exist
SELECT 
    t.typname as enum_name,
    array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE t.typname IN (
    'approval_status', 'assignment_status', 'driver_status', 'location_type',
    'recurrence_pattern', 'scan_type', 'shift_status', 'shift_type',
    'trip_status', 'truck_status', 'user_role'
)
GROUP BY t.typname
ORDER BY t.typname;

-- ============================================================================
-- TABLE STRUCTURE ANALYSIS (17 total)
-- ============================================================================

-- Query to verify all tables exist with correct column counts
SELECT 
    schemaname,
    tablename,
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_name = tablename AND table_schema = schemaname) as column_count
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN (
        'approvals', 'assignments', 'automated_fix_logs', 'driver_shifts',
        'drivers', 'dump_trucks', 'health_check_logs', 'locations',
        'migration_log', 'migrations', 'scan_logs', 'shift_handovers',
        'system_health_logs', 'system_logs', 'system_tasks', 'trip_logs', 'users'
    )
ORDER BY tablename;

-- ============================================================================
-- FUNCTION ANALYSIS (57+ total)
-- ============================================================================

-- Query to list all custom functions (excluding system functions)
SELECT 
    n.nspname as schema_name,
    p.proname as function_name,
    pg_get_function_identity_arguments(p.oid) as arguments,
    pg_get_function_result(p.oid) as return_type
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
    AND p.proname NOT LIKE 'pg_%'
    AND p.proname NOT LIKE 'information_schema_%'
ORDER BY p.proname;

-- ============================================================================
-- TRIGGER ANALYSIS (13 total)
-- ============================================================================

-- Query to verify all triggers exist
SELECT 
    t.tgname as trigger_name,
    c.relname as table_name,
    p.proname as function_name,
    CASE t.tgtype & 66
        WHEN 2 THEN 'BEFORE'
        WHEN 64 THEN 'INSTEAD OF'
        ELSE 'AFTER'
    END as trigger_timing,
    CASE t.tgtype & 28
        WHEN 4 THEN 'INSERT'
        WHEN 8 THEN 'DELETE'
        WHEN 16 THEN 'UPDATE'
        WHEN 12 THEN 'INSERT OR DELETE'
        WHEN 20 THEN 'INSERT OR UPDATE'
        WHEN 24 THEN 'DELETE OR UPDATE'
        WHEN 28 THEN 'INSERT OR DELETE OR UPDATE'
    END as trigger_events
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE NOT t.tgisinternal
ORDER BY c.relname, t.tgname;

-- ============================================================================
-- VIEW ANALYSIS (6 regular + 4 materialized)
-- ============================================================================

-- Query to verify all views exist
SELECT 
    schemaname,
    viewname,
    'regular' as view_type
FROM pg_views 
WHERE schemaname = 'public'
UNION ALL
SELECT 
    schemaname,
    matviewname as viewname,
    'materialized' as view_type
FROM pg_matviews 
WHERE schemaname = 'public'
ORDER BY view_type, viewname;

-- ============================================================================
-- INDEX ANALYSIS
-- ============================================================================

-- Query to verify all indexes exist
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
    AND indexname NOT LIKE '%_pkey'  -- Exclude primary keys for brevity
ORDER BY tablename, indexname;

-- ============================================================================
-- CONSTRAINT ANALYSIS
-- ============================================================================

-- Query to verify all constraints exist
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    CASE 
        WHEN tc.constraint_type = 'FOREIGN KEY' THEN
            (SELECT ccu.table_name || '(' || ccu.column_name || ')'
             FROM information_schema.constraint_column_usage ccu
             WHERE ccu.constraint_name = tc.constraint_name)
        ELSE NULL
    END as references
FROM information_schema.table_constraints tc
WHERE tc.table_schema = 'public'
    AND tc.constraint_type IN ('PRIMARY KEY', 'FOREIGN KEY', 'UNIQUE', 'CHECK')
ORDER BY tc.table_name, tc.constraint_type, tc.constraint_name;

-- ============================================================================
-- SCHEMA COMPLETENESS VALIDATION
-- ============================================================================

-- Summary query for consolidation validation
SELECT 
    'Tables' as component,
    COUNT(*) as count
FROM pg_tables 
WHERE schemaname = 'public'
UNION ALL
SELECT 
    'Functions' as component,
    COUNT(*) as count
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
    AND p.proname NOT LIKE 'pg_%'
UNION ALL
SELECT 
    'Triggers' as component,
    COUNT(*) as count
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
    AND NOT t.tgisinternal
UNION ALL
SELECT 
    'Views' as component,
    COUNT(*) as count
FROM pg_views 
WHERE schemaname = 'public'
UNION ALL
SELECT 
    'Materialized Views' as component,
    COUNT(*) as count
FROM pg_matviews 
WHERE schemaname = 'public'
UNION ALL
SELECT 
    'Enums' as component,
    COUNT(DISTINCT t.typname) as count
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid
ORDER BY component;