import React, { useState, useEffect } from 'react';
import { getApiBaseUrl } from '../../../utils/network-utils';
import TaskManagementPanel from './TaskManagementPanel';
import CleanupManagementPanel from './CleanupManagementPanel';

const SystemHealthMonitor = () => {
  const [healthStatus, setHealthStatus] = useState({
    shifts: { status: 'loading', issues: [], lastCheck: null },
    assignments: { status: 'loading', issues: [], lastCheck: null },
    trips: { status: 'loading', issues: [], lastCheck: null },
    database: { status: 'loading', issues: [], lastCheck: null }
  });

  const [isFixing, setIsFixing] = useState({
    shifts: false,
    assignments: false,
    trips: false,
    database: false
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [expandedModules, setExpandedModules] = useState({});

  // Fetch health status
  const fetchHealthStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch health status: ${response.status}`);
      }

      const result = await response.json();

      // Check if we got actual data or need to use fallback
      if (result && (result.shifts || result.assignments || result.trips || result.database)) {
        setHealthStatus(result);
      } else {
        // Use fallback data only if no real data is available
        setHealthStatus(result.data || {
          shifts: {
            status: 'warning',
            issues: [{
              id: 'api-not-implemented',
              type: 'api_not_implemented',
              severity: 'medium',
              description: 'System Health API not yet implemented. Backend endpoints need to be created.',
              affectedRecords: [],
              autoFixable: false
            }],
            lastCheck: new Date().toISOString()
          },
          assignments: {
            status: 'warning',
            issues: [{
              id: 'api-not-implemented',
              type: 'api_not_implemented',
              severity: 'medium',
              description: 'System Health API not yet implemented. Backend endpoints need to be created.',
              affectedRecords: [],
              autoFixable: false
            }],
            lastCheck: new Date().toISOString()
          },
          trips: {
            status: 'warning',
            issues: [{
              id: 'api-not-implemented',
              type: 'api_not_implemented',
              severity: 'medium',
              description: 'System Health API not yet implemented. Backend endpoints need to be created.',
              affectedRecords: [],
              autoFixable: false
            }],
            lastCheck: new Date().toISOString()
          },
          database: {
            status: 'warning',
            issues: [{
              id: 'api-not-implemented',
              type: 'api_not_implemented',
              severity: 'medium',
              description: 'Database Health API not yet implemented. Backend endpoints need to be created.',
              affectedRecords: [],
              autoFixable: false
            }],
            lastCheck: new Date().toISOString()
          }
        });
      }

    } catch (err) {
      console.error('Error fetching health status:', err);
      setError(err.message);

      // Set placeholder data when API fails
      setHealthStatus({
        shifts: {
          status: 'warning',
          issues: [{
            id: 'api-error',
            type: 'api_error',
            severity: 'medium',
            description: `Error fetching health data: ${err.message}`,
            affectedRecords: [],
            autoFixable: false
          }],
          lastCheck: new Date().toISOString()
        },
        assignments: {
          status: 'warning',
          issues: [{
            id: 'api-error',
            type: 'api_error',
            severity: 'medium',
            description: `Error fetching health data: ${err.message}`,
            affectedRecords: [],
            autoFixable: false
          }],
          lastCheck: new Date().toISOString()
        },
        trips: {
          status: 'warning',
          issues: [{
            id: 'api-error',
            type: 'api_error',
            severity: 'medium',
            description: `Error fetching health data: ${err.message}`,
            affectedRecords: [],
            autoFixable: false
          }],
          lastCheck: new Date().toISOString()
        },
        database: {
          status: 'warning',
          issues: [{
            id: 'api-error',
            type: 'api_error',
            severity: 'medium',
            description: `Error fetching health data: ${err.message}`,
            affectedRecords: [],
            autoFixable: false
          }],
          lastCheck: new Date().toISOString()
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // Fix function for each module
  const handleFix = async (module) => {
    try {
      setIsFixing(prev => ({ ...prev, [module]: true }));
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/system-health/fix-${module}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fix ${module}: ${response.status}`);
      }

      const result = await response.json();

      // Refresh health status after fix
      await fetchHealthStatus();

      // Show success message (you could add a toast notification here)
      console.log(`${module} fix completed:`, result);

    } catch (err) {
      console.error(`Error fixing ${module}:`, err);
      setError(`Failed to fix ${module}: ${err.message}`);
    } finally {
      setIsFixing(prev => ({ ...prev, [module]: false }));
    }
  };

  // Toggle module expansion
  const toggleModuleExpansion = (moduleName) => {
    setExpandedModules(prev => ({
      ...prev,
      [moduleName]: !prev[moduleName]
    }));
  };

  // Auto-refresh effect
  useEffect(() => {
    fetchHealthStatus();

    if (autoRefresh) {
      const interval = setInterval(fetchHealthStatus, 30000); // 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Status icon helper
  const getStatusIcon = (status) => {
    switch (status) {
      case 'operational': return '✅';
      case 'warning': return '⚠️';
      case 'critical': return '❌';
      default: return '⏳';
    }
  };

  // Status color helper
  const getStatusColor = (status) => {
    switch (status) {
      case 'operational': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Render module status with mobile-first design and accessibility
  const renderModuleStatus = (moduleName, moduleData) => {
    const isExpanded = expandedModules[moduleName];
    const hasIssues = moduleData.issues?.length > 0;

    return (
      <div
        key={moduleName}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4"
        role="region"
        aria-labelledby={`${moduleName}-heading`}
      >
        {/* Module Header - Mobile Optimized */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-3">
          <button
            id={`${moduleName}-heading`}
            onClick={() => toggleModuleExpansion(moduleName)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleModuleExpansion(moduleName);
              }
            }}
            className="flex items-center space-x-2 text-left w-full sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded p-1 -m-1"
            aria-expanded={isExpanded}
            aria-controls={`${moduleName}-content`}
            aria-describedby={`${moduleName}-status`}
          >
            <span className="text-lg sm:text-xl" role="img" aria-label={`${moduleName} status`}>
              {getStatusIcon(moduleData.status)}
            </span>
            <h3 className="text-base sm:text-lg font-semibold capitalize">{moduleName}</h3>
            <span
              id={`${moduleName}-status`}
              className={`text-xs sm:text-sm font-medium px-2 py-1 rounded ${getStatusColor(moduleData.status)} bg-opacity-10`}
            >
              {moduleData.status?.toUpperCase() || 'LOADING'}
            </span>
            <span className="ml-auto text-gray-400" aria-hidden="true">
              {isExpanded ? '▼' : '▶'}
            </span>
          </button>

          {/* Fix Button - Touch Friendly */}
          {hasIssues && (
            <button
              onClick={() => handleFix(moduleName)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleFix(moduleName);
                }
              }}
              disabled={isFixing[moduleName] || loading}
              className="min-h-[44px] px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors w-full sm:w-auto"
              aria-describedby={`${moduleName}-fix-description`}
            >
              {isFixing[moduleName] ? (
                <>
                  <span className="inline-block animate-spin mr-2" aria-hidden="true">⟳</span>
                  Fixing...
                </>
              ) : (
                'Fix Issues'
              )}
            </button>
          )}
        </div>

        {/* Module Content - Collapsible */}
        <div
          id={`${moduleName}-content`}
          className={`transition-all duration-200 ${isExpanded ? 'block' : 'hidden'}`}
          aria-hidden={!isExpanded}
        >
          {hasIssues && (
            <div className="space-y-3" role="list" aria-label={`${moduleName} issues`}>
              <span
                id={`${moduleName}-fix-description`}
                className="sr-only"
              >
                Fix {moduleData.issues.length} issue{moduleData.issues.length !== 1 ? 's' : ''} in {moduleName} module
              </span>

              {moduleData.issues.map((issue, index) => (
                <div
                  key={issue.id || index}
                  className="bg-gray-50 rounded-lg p-3 text-sm"
                  role="listitem"
                  aria-labelledby={`issue-${moduleName}-${index}-title`}
                >
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                    <span
                      id={`issue-${moduleName}-${index}-title`}
                      className="font-medium text-gray-700 text-sm sm:text-base"
                    >
                      {issue.type?.replace('_', ' ') || 'Issue'}
                    </span>
                    <span
                      className={`inline-flex px-2 py-1 rounded-full text-xs font-medium self-start sm:self-auto ${issue.severity === 'critical' ? 'bg-red-100 text-red-800' :
                        issue.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}
                      aria-label={`Severity: ${issue.severity || 'unknown'}`}
                    >
                      {issue.severity || 'unknown'}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed mb-2">
                    {issue.description}
                  </p>
                  {issue.affectedRecords?.length > 0 && (
                    <p className="text-xs text-gray-500">
                      <span className="font-medium">Affected records:</span> {issue.affectedRecords.length}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}

          {!hasIssues && (
            <div className="text-center py-4 text-gray-500">
              <span className="text-2xl mb-2 block" role="img" aria-label="All good">✅</span>
              <p className="text-sm">No issues detected</p>
            </div>
          )}

          {moduleData.lastCheck && (
            <div className="mt-4 pt-3 border-t border-gray-100 text-xs text-gray-500">
              <span className="font-medium">Last checked:</span> {new Date(moduleData.lastCheck).toLocaleString()}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header - Mobile Responsive */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">System Health Monitor</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">Monitor and maintain system health across all modules</p>
        </div>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <label className="flex items-center justify-center sm:justify-start space-x-2 min-h-[44px] px-3 py-2 sm:px-0 sm:py-0">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
              aria-describedby="auto-refresh-description"
            />
            <span className="text-sm text-gray-700">Auto-refresh</span>
            <span id="auto-refresh-description" className="sr-only">
              Automatically refresh system health status every 30 seconds
            </span>
          </label>

          <button
            onClick={fetchHealthStatus}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                fetchHealthStatus();
              }
            }}
            disabled={loading}
            className="min-h-[44px] px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            aria-describedby="refresh-description"
          >
            {loading ? (
              <>
                <span className="inline-block animate-spin mr-2" aria-hidden="true">⟳</span>
                Refreshing...
              </>
            ) : (
              'Refresh Status'
            )}
            <span id="refresh-description" className="sr-only">
              Manually refresh system health status for all modules
            </span>
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-red-600 mr-2">❌</span>
            <span className="text-red-800 font-medium">Error:</span>
            <span className="text-red-700 ml-2">{error}</span>
          </div>
        </div>
      )}

      {/* System Health Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(healthStatus).map(([moduleName, moduleData]) =>
          renderModuleStatus(moduleName, moduleData)
        )}
      </div>

      {/* Task Management Panel */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200 px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-900">Task Management</h3>
          <p className="text-sm text-gray-600 mt-1">Manage maintenance tasks and system recommendations</p>
        </div>
        <div className="p-6">
          <TaskManagementPanel />
        </div>
      </div>

      {/* Cleanup Management Panel */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200 px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-900">System Cleanup</h3>
          <p className="text-sm text-gray-600 mt-1">Analyze and clean up unused code and resources</p>
        </div>
        <div className="p-6">
          <CleanupManagementPanel />
        </div>
      </div>
    </div>
  );
};

export default SystemHealthMonitor;