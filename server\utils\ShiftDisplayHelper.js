/**
 * Shift Display Helper
 * Simple utility to get current driver for display purposes only
 * Does NOT affect the 4-phase workflow or scanner logic
 */

const { query } = require('../config/database');

// Cache for current drivers to prevent excessive database queries
const driverCache = new Map();
const CACHE_TTL = 30000; // 30 seconds cache TTL
const REQUEST_THROTTLE = new Map();
const THROTTLE_WINDOW = 1000; // 1 second throttle window

class ShiftDisplayHelper {
  
  /**
   * Get current active driver for display in Assignment Management and Trip Monitoring
   * This is purely for display purposes and doesn't affect workflow
   */
  static async getCurrentDriverForDisplay(truckId) {
    try {
      // Check cache first
      const cacheKey = `driver_${truckId}`;
      const cached = driverCache.get(cacheKey);

      if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
        return cached.data;
      }

      // Throttle requests to prevent excessive queries
      const throttleKey = `throttle_${truckId}`;
      const lastRequest = REQUEST_THROTTLE.get(throttleKey);

      if (lastRequest && (Date.now() - lastRequest) < THROTTLE_WINDOW) {
        // Return cached data if available, otherwise return empty result
        return cached ? cached.data : {
          hasActiveShift: false,
          driver_id: null,
          driver_name: null,
          employee_id: null,
          shift_type: null,
          shift_id: null
        };
      }

      REQUEST_THROTTLE.set(throttleKey, Date.now());

      const currentDriverQuery = `
        SELECT
          ds.id as shift_id,
          ds.driver_id,
          ds.shift_type,
          ds.display_type,
          ds.start_time,
          ds.end_time,
          d.full_name as driver_name,
          d.employee_id
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.status = 'active'
          AND (
            (ds.recurrence_pattern = 'single' AND ds.shift_date = CURRENT_DATE)
            OR
            (ds.recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date)
          )
          AND (
            -- For regular shifts (same day)
            (ds.end_time >= ds.start_time AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (ds.end_time < ds.start_time AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
          )
        ORDER BY ds.created_at DESC
        LIMIT 1
      `;

      const result = await query(currentDriverQuery, [truckId]);

      let driverData;
      if (result.rows.length > 0) {
        driverData = {
          hasActiveShift: true,
          driver_id: result.rows[0].driver_id,
          driver_name: result.rows[0].driver_name,
          employee_id: result.rows[0].employee_id,
          shift_type: result.rows[0].shift_type,
          display_type: result.rows[0].display_type,
          shift_id: result.rows[0].shift_id
        };
      } else {
        driverData = {
          hasActiveShift: false,
          driver_id: null,
          driver_name: null,
          employee_id: null,
          shift_type: null,
          shift_id: null
        };
      }

      // Cache the result
      driverCache.set(cacheKey, {
        data: driverData,
        timestamp: Date.now()
      });

      return driverData;

    } catch (error) {
      console.error('Error getting current driver for display:', error);
      return {
        hasActiveShift: false,
        driver_id: null,
        driver_name: null,
        employee_id: null,
        shift_type: null,
        shift_id: null,
        error: error.message
      };
    }
  }

  /**
   * Get current drivers for multiple trucks (for bulk display)
   */
  static async getCurrentDriversForDisplay(truckIds) {
    try {
      if (!truckIds || truckIds.length === 0) {
        return {};
      }

      // Check cache for all truck IDs
      const cacheKey = `drivers_${truckIds.sort().join(',')}`;
      const cached = driverCache.get(cacheKey);

      if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
        return cached.data;
      }

      // Throttle bulk requests
      const throttleKey = `bulk_throttle_${truckIds.length}`;
      const lastRequest = REQUEST_THROTTLE.get(throttleKey);

      if (lastRequest && (Date.now() - lastRequest) < THROTTLE_WINDOW) {
        // Return cached data if available, otherwise return empty result
        return cached ? cached.data : {};
      }

      REQUEST_THROTTLE.set(throttleKey, Date.now());

      const placeholders = truckIds.map((_, index) => `$${index + 1}`).join(',');

      const currentDriversQuery = `
        SELECT
          ds.truck_id,
          ds.id as shift_id,
          ds.driver_id,
          ds.shift_type,
          ds.display_type,
          ds.start_time,
          ds.end_time,
          d.full_name as driver_name,
          d.employee_id
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id IN (${placeholders})
          AND ds.status = 'active'
          AND (
            (ds.recurrence_pattern = 'single' AND ds.shift_date = CURRENT_DATE)
            OR
            (ds.recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date)
          )
          AND (
            -- For regular shifts (same day)
            (ds.end_time >= ds.start_time AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
            OR
            -- For overnight shifts (crosses midnight)
            (ds.end_time < ds.start_time AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
          )
        ORDER BY ds.truck_id, ds.created_at DESC
      `;

      const result = await query(currentDriversQuery, truckIds);

      // Group by truck_id and take the most recent shift for each truck
      const driversByTruck = {};
      const processedTrucks = new Set();

      result.rows.forEach(row => {
        if (!processedTrucks.has(row.truck_id)) {
          driversByTruck[row.truck_id] = {
            hasActiveShift: true,
            driver_id: row.driver_id,
            driver_name: row.driver_name,
            employee_id: row.employee_id,
            shift_type: row.shift_type,
            display_type: row.display_type,
            shift_id: row.shift_id
          };
          processedTrucks.add(row.truck_id);
        }
      });

      // Add empty entries for trucks without active shifts
      truckIds.forEach(truckId => {
        if (!driversByTruck[truckId]) {
          driversByTruck[truckId] = {
            hasActiveShift: false,
            driver_id: null,
            driver_name: null,
            employee_id: null,
            shift_type: null,
            shift_id: null
          };
        }
      });

      // Cache the result
      driverCache.set(cacheKey, {
        data: driversByTruck,
        timestamp: Date.now()
      });

      return driversByTruck;

    } catch (error) {
      console.error('Error getting current drivers for display:', error);
      return {};
    }
  }

  /**
   * Format driver display text for UI
   */
  static formatDriverDisplay(driverInfo, originalDriverName = null) {
    if (!driverInfo) {
      return originalDriverName || 'Unknown Driver';
    }

    if (driverInfo.hasActiveShift) {
      // Use display_type if available (intelligent classification), otherwise use shift_type
      const displayType = driverInfo.display_type || driverInfo.shift_type;
      const shiftIndicator = displayType === 'day' ? '☀️' :
                           displayType === 'night' ? '🌙' : '🔧';
      return `${shiftIndicator} ${driverInfo.driver_name} (${displayType})`;
    }

    return originalDriverName || 'No Active Shift';
  }

  /**
   * Get shift status for display
   */
  static getShiftStatusDisplay(driverInfo) {
    if (!driverInfo || !driverInfo.hasActiveShift) {
      return {
        status: 'no_shift',
        text: 'No Active Shift',
        color: 'text-gray-500 bg-gray-100'
      };
    }

    const shiftTypes = {
      'day': {
        status: 'day_shift',
        text: 'Day Shift',
        color: 'text-yellow-700 bg-yellow-100'
      },
      'night': {
        status: 'night_shift',
        text: 'Night Shift',
        color: 'text-blue-700 bg-blue-100'
      },
      'custom': {
        status: 'custom_shift',
        text: 'Custom Shift',
        color: 'text-purple-700 bg-purple-100'
      }
    };

    // Use display_type if available (intelligent classification), otherwise use shift_type
    const displayType = driverInfo.display_type || driverInfo.shift_type;

    return shiftTypes[displayType] || {
      status: 'unknown_shift',
      text: 'Unknown Shift',
      color: 'text-gray-700 bg-gray-100'
    };
  }

  /**
   * Clear expired cache entries to prevent memory leaks
   */
  static clearExpiredCache() {
    const now = Date.now();
    for (const [key, value] of driverCache.entries()) {
      if (now - value.timestamp > CACHE_TTL) {
        driverCache.delete(key);
      }
    }

    // Clear old throttle entries
    for (const [key, timestamp] of REQUEST_THROTTLE.entries()) {
      if (now - timestamp > THROTTLE_WINDOW * 2) {
        REQUEST_THROTTLE.delete(key);
      }
    }
  }

  /**
   * Clear all cache (for testing or manual refresh)
   */
  static clearAllCache() {
    driverCache.clear();
    REQUEST_THROTTLE.clear();
  }

  /**
   * Get cache information for monitoring
   */
  static getCacheInfo() {
    return {
      driverCache: driverCache.size,
      requestThrottle: REQUEST_THROTTLE.size,
      cacheTTL: CACHE_TTL,
      throttleWindow: THROTTLE_WINDOW
    };
  }
}

// Periodic cache cleanup to prevent memory leaks
setInterval(() => {
  ShiftDisplayHelper.clearExpiredCache();
}, CACHE_TTL);

module.exports = ShiftDisplayHelper;
