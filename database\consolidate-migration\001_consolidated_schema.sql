-- ============================================================================
-- CONSOLIDATED SCHEMA MIGRATION v1.0
-- Hauling QR Trip Management System
-- ============================================================================
-- 
-- This migration consolidates all historical migrations (001-064) into a single,
-- authoritative schema definition. It creates a complete database structure
-- identical to the production database with enhanced capabilities.
--
-- Source Authority:
-- - Primary: database/init-new.sql (pg_dump from production hauling_qr_system)
-- - Enhanced: database/init.sql v4.0 (69 functions, additional features)
-- - Historical: database/migrations/001-064 (migration evolution context)
--
-- Generated: 2025-01-19
-- Target Database: hauling_qr_system
-- Migration Type: Consolidation (replaces migrations 001-064)
-- 
-- IMPORTANT: This migration is designed to work with both:
-- 1. Fresh database installations (no existing tables)
-- 2. Existing databases (will be handled by migration_tracker.sql)
--
-- ============================================================================

BEGIN;

-- Set transaction isolation level for consistency
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ============================================================================
-- ENUM TYPES
-- ============================================================================
-- These enum types provide type safety and ensure data consistency across
-- the entire system. Order matters for dependencies.

-- User roles for authentication and authorization
CREATE TYPE user_role AS ENUM ('admin', 'supervisor', 'operator');
COMMENT ON TYPE user_role IS 'User roles for system access control';

-- Truck operational status
CREATE TYPE truck_status AS ENUM ('active', 'inactive', 'maintenance', 'retired');
COMMENT ON TYPE truck_status IS 'Operational status of dump trucks';

-- Driver employment status  
CREATE TYPE driver_status AS ENUM ('active', 'inactive', 'on_leave', 'terminated');
COMMENT ON TYPE driver_status IS 'Employment status of drivers';

-- Location types for workflow management
CREATE TYPE location_type AS ENUM ('loading', 'unloading', 'checkpoint');
COMMENT ON TYPE location_type IS 'Types of locations in the hauling workflow';

-- Assignment workflow status
CREATE TYPE assignment_status AS ENUM ('pending_approval', 'assigned', 'in_progress', 'completed', 'cancelled');
COMMENT ON TYPE assignment_status IS 'Status of truck-driver-route assignments';

-- Trip workflow status (includes all historical statuses)
CREATE TYPE trip_status AS ENUM (
    'assigned',
    'loading_start', 
    'loading_end',
    'unloading_start',
    'unloading_end', 
    'trip_completed',
    'stopped',
    'cancelled'
);
COMMENT ON TYPE trip_status IS 'Status of individual trips through the 4-phase workflow';

-- Exception approval workflow status
CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected');
COMMENT ON TYPE approval_status IS 'Status of exception approval requests';

-- QR code scan types for audit trail
CREATE TYPE scan_type AS ENUM ('location_scan', 'truck_scan', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end');
COMMENT ON TYPE scan_type IS 'Types of QR code scans for audit logging';

-- Shift types for multi-driver support
CREATE TYPE shift_type AS ENUM ('day', 'night', 'custom');
COMMENT ON TYPE shift_type IS 'Types of driver shifts for multi-driver truck operation';

-- Shift status for automatic activation
CREATE TYPE shift_status AS ENUM ('scheduled', 'active', 'completed', 'cancelled');
COMMENT ON TYPE shift_status IS 'Status of driver shifts with automatic time-based transitions';

-- Recurrence pattern for shift scheduling
CREATE TYPE recurrence_pattern AS ENUM ('single', 'daily', 'weekly', 'weekdays', 'weekends', 'custom');
COMMENT ON TYPE recurrence_pattern IS 'Pattern for recurring shift schedules';-- ============================================================================
-- CONSOLIDATED SCHEMA MIGRATION v1.0
-- Hauling QR Trip Management System
-- ============================================================================
-- 
-- This migration consolidates all historical migrations (001-064) into a single,
-- authoritative schema definition. It creates a complete database structure
-- identical to the production database with enhanced capabilities.
--
-- Source Authority:
-- - Primary: database/init-new.sql (pg_dump from production hauling_qr_system)
-- - Enhanced: database/init.sql v4.0 (69 functions, additional features)
-- - Historical: database/migrations/001-064 (migration evolution context)
--
-- Generated: 2025-01-19
-- Target Database: hauling_qr_system
-- Migration Type: Consolidation (replaces migrations 001-064)
-- 
-- IMPORTANT: This migration is designed to work with both:
-- 1. Fresh database installations (no existing tables)
-- 2. Existing databases (will be handled by migration_tracker.sql)
--
-- ============================================================================

BEGIN;

-- Set transaction isolation level for consistency
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ============================================================================
-- ENUM TYPES
-- ============================================================================
-- These enum types provide type safety and ensure data consistency across
-- the entire system. Order matters for dependencies.

-- User roles for authentication and authorization
CREATE TYPE user_role AS ENUM ('admin', 'supervisor', 'operator');
COMMENT ON TYPE user_role IS 'User roles for system access control';

-- Truck operational status
CREATE TYPE truck_status AS ENUM ('active', 'inactive', 'maintenance', 'retired');
COMMENT ON TYPE truck_status IS 'Operational status of dump trucks';

-- Driver employment status  
CREATE TYPE driver_status AS ENUM ('active', 'inactive', 'on_leave', 'terminated');
COMMENT ON TYPE driver_status IS 'Employment status of drivers';

-- Location types for workflow management
CREATE TYPE location_type AS ENUM ('loading', 'unloading', 'checkpoint');
COMMENT ON TYPE location_type IS 'Types of locations in the hauling workflow';

-- Assignment workflow status
CREATE TYPE assignment_status AS ENUM ('pending_approval', 'assigned', 'in_progress', 'completed', 'cancelled');
COMMENT ON TYPE assignment_status IS 'Status of truck-driver-route assignments';

-- Trip workflow status (includes all historical statuses)
CREATE TYPE trip_status AS ENUM (
    'assigned',
    'loading_start', 
    'loading_end',
    'unloading_start',
    'unloading_end', 
    'trip_completed',
    'stopped',
    'cancelled'
);
COMMENT ON TYPE trip_status IS 'Status of individual trips through the 4-phase workflow';

-- Exception approval workflow status
CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected');
COMMENT ON TYPE approval_status IS 'Status of exception approval requests';

-- QR code scan types for audit trail
CREATE TYPE scan_type AS ENUM ('location_scan', 'truck_scan', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end');
COMMENT ON TYPE scan_type IS 'Types of QR code scans for audit logging';

-- Shift types for multi-driver support
CREATE TYPE shift_type AS ENUM ('day', 'night', 'custom');
COMMENT ON TYPE shift_type IS 'Types of driver shifts for multi-driver truck operation';

-- Shift status for automatic activation
CREATE TYPE shift_status AS ENUM ('scheduled', 'active', 'completed', 'cancelled');
COMMENT ON TYPE shift_status IS 'Status of driver shifts with automatic time-based transitions';

-- Recurrence pattern for shift scheduling
CREATE TYPE recurrence_pattern AS ENUM ('single', 'daily', 'weekly', 'weekdays', 'weekends', 'custom');
COMMENT ON TYPE recurrence_pattern IS 'Pattern for recurring shift schedules';

-- ============================================================================
-- CORE TABLES
-- ============================================================================
-- Tables are ordered to respect foreign key dependencies

-- ============================================================================
-- TABLE: users
-- Purpose: User authentication and role management
-- Historical: Base table from migration 001, enhanced through multiple migrations
-- ============================================================================

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role user_role NOT NULL DEFAULT 'operator',
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE users IS 'User accounts for system authentication and authorization';
COMMENT ON COLUMN users.username IS 'Unique username for login';
COMMENT ON COLUMN users.email IS 'User email address for notifications';
COMMENT ON COLUMN users.password_hash IS 'Hashed password for security';
COMMENT ON COLUMN users.role IS 'User role determining system permissions';
COMMENT ON COLUMN users.is_active IS 'Whether the user account is active';

-- ============================================================================
-- TABLE: dump_trucks  
-- Purpose: Fleet management and truck tracking
-- Historical: Base table from migration 001, enhanced with status tracking
-- ============================================================================

CREATE TABLE dump_trucks (
    id SERIAL PRIMARY KEY,
    truck_number VARCHAR(20) NOT NULL UNIQUE,
    license_plate VARCHAR(20) NOT NULL UNIQUE,
    make VARCHAR(50),
    model VARCHAR(50),
    year INTEGER,
    capacity_tons DECIMAL(5,2),
    status truck_status NOT NULL DEFAULT 'active',
    last_maintenance DATE,
    next_maintenance DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE dump_trucks IS 'Fleet of dump trucks used for hauling operations';
COMMENT ON COLUMN dump_trucks.truck_number IS 'Unique identifier for the truck';
COMMENT ON COLUMN dump_trucks.license_plate IS 'Vehicle license plate number';
COMMENT ON COLUMN dump_trucks.capacity_tons IS 'Maximum load capacity in tons';
COMMENT ON COLUMN dump_trucks.status IS 'Current operational status of the truck';

-- ============================================================================
-- TABLE: drivers
-- Purpose: Driver information and license management  
-- Historical: Base table from migration 001, enhanced with licensing details
-- ============================================================================

CREATE TABLE drivers (
    id SERIAL PRIMARY KEY,
    employee_id VARCHAR(20) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    license_number VARCHAR(30) NOT NULL UNIQUE,
    license_expiry DATE NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    status driver_status NOT NULL DEFAULT 'active',
    hire_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE drivers IS 'Driver information and licensing details';
COMMENT ON COLUMN drivers.employee_id IS 'Company employee identification number';
COMMENT ON COLUMN drivers.license_number IS 'Commercial driver license number';
COMMENT ON COLUMN drivers.license_expiry IS 'License expiration date for compliance tracking';
COMMENT ON COLUMN drivers.status IS 'Current employment status of the driver';-- ============================================================================
-- TABLE: locations
-- Purpose: Loading, unloading, and checkpoint location management
-- Historical: Base table from migration 001, enhanced with GPS and workflow types
-- ============================================================================

CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    location_code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    type location_type NOT NULL,
    address TEXT,
    gps_latitude DECIMAL(10, 8),
    gps_longitude DECIMAL(11, 8),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    operating_hours VARCHAR(100),
    special_instructions TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE locations IS 'Loading, unloading, and checkpoint locations for hauling operations';
COMMENT ON COLUMN locations.location_code IS 'Unique code for QR generation and identification';
COMMENT ON COLUMN locations.type IS 'Type of location in the hauling workflow';
COMMENT ON COLUMN locations.gps_latitude IS 'GPS latitude for location verification';
COMMENT ON COLUMN locations.gps_longitude IS 'GPS longitude for location verification';

-- ============================================================================
-- TABLE: assignments
-- Purpose: Truck-driver-route assignment management
-- Historical: Core table enhanced through migrations 001-064 with priority, auto-creation, adaptive features
-- ============================================================================

CREATE TABLE assignments (
    id SERIAL PRIMARY KEY,
    assignment_code VARCHAR(50),
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER REFERENCES drivers(id) ON DELETE SET NULL,
    loading_location_id INTEGER NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
    unloading_location_id INTEGER NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
    assigned_date DATE DEFAULT CURRENT_DATE,
    status assignment_status NOT NULL DEFAULT 'assigned',
    priority VARCHAR(20) DEFAULT 'normal',
    expected_loads_per_day INTEGER DEFAULT 1,
    notes TEXT,
    is_adaptive BOOLEAN DEFAULT false,
    auto_created BOOLEAN DEFAULT false,
    is_shift_assignment BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraint from migration 044: Ensure driver assignment integrity
    CONSTRAINT chk_assignment_integrity CHECK (
        (driver_id IS NOT NULL) OR 
        (is_shift_assignment = true) OR 
        (auto_created = true)
    )
);

COMMENT ON TABLE assignments IS 'Truck-driver-route assignments for hauling operations';
COMMENT ON COLUMN assignments.assignment_code IS 'Human-readable assignment identifier';
COMMENT ON COLUMN assignments.priority IS 'Assignment priority level (normal, high, urgent)';
COMMENT ON COLUMN assignments.expected_loads_per_day IS 'Expected number of loads for planning';
COMMENT ON COLUMN assignments.is_adaptive IS 'Whether assignment was created by adaptive system';
COMMENT ON COLUMN assignments.auto_created IS 'Whether assignment was auto-created by system';
COMMENT ON COLUMN assignments.is_shift_assignment IS 'Whether assignment uses shift-based driver allocation';

-- ============================================================================
-- TABLE: trip_logs
-- Purpose: Individual trip tracking through 4-phase workflow
-- Historical: Core table enhanced through migrations with duration tracking, stop handling, driver capture
-- ============================================================================

CREATE TABLE trip_logs (
    id SERIAL PRIMARY KEY,
    assignment_id INTEGER NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
    trip_number INTEGER NOT NULL,
    status trip_status NOT NULL DEFAULT 'assigned',
    
    -- Phase timestamps for 4-phase workflow
    loading_start_time TIMESTAMP WITH TIME ZONE,
    loading_end_time TIMESTAMP WITH TIME ZONE,
    unloading_start_time TIMESTAMP WITH TIME ZONE,
    unloading_end_time TIMESTAMP WITH TIME ZONE,
    
    -- Actual locations (for route deviation tracking)
    actual_loading_location_id INTEGER REFERENCES locations(id),
    actual_unloading_location_id INTEGER REFERENCES locations(id),
    
    -- Duration and performance metrics
    total_duration_minutes INTEGER,
    loading_duration_minutes INTEGER,
    travel_duration_minutes INTEGER,
    unloading_duration_minutes INTEGER,
    
    -- Exception and workflow tracking
    is_exception BOOLEAN DEFAULT false,
    workflow_type VARCHAR(50) DEFAULT 'standard',
    location_sequence TEXT[],
    
    -- Driver information (captured at trip creation for historical accuracy)
    performed_by_driver_id INTEGER,
    performed_by_driver_name VARCHAR(100),
    performed_by_employee_id VARCHAR(20),
    performed_by_shift_id INTEGER,
    performed_by_shift_type shift_type,
    
    -- Stop handling (from migration 027, 039-043)
    stopped_reported_at TIMESTAMP WITH TIME ZONE,
    stopped_reason TEXT,
    stopped_resolved_at TIMESTAMP WITH TIME ZONE,
    stopped_resolved_by INTEGER REFERENCES users(id),
    previous_status trip_status,
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure trip number uniqueness per assignment
    UNIQUE(assignment_id, trip_number)
);

COMMENT ON TABLE trip_logs IS 'Individual trip tracking through the 4-phase hauling workflow';
COMMENT ON COLUMN trip_logs.trip_number IS 'Sequential trip number within the assignment';
COMMENT ON COLUMN trip_logs.status IS 'Current phase of the trip workflow';
COMMENT ON COLUMN trip_logs.total_duration_minutes IS 'Total trip duration from start to completion';
COMMENT ON COLUMN trip_logs.is_exception IS 'Whether trip involved exception handling';
COMMENT ON COLUMN trip_logs.workflow_type IS 'Type of workflow used (standard, multi-location, etc.)';
COMMENT ON COLUMN trip_logs.location_sequence IS 'Sequence of locations visited during trip';
COMMENT ON COLUMN trip_logs.performed_by_driver_id IS 'Driver ID captured at trip creation (immutable)';
COMMENT ON COLUMN trip_logs.performed_by_driver_name IS 'Driver name captured at trip creation (immutable)';
COMMENT ON COLUMN trip_logs.stopped_reason IS 'Reason for trip stop/breakdown';-- ============================================================================
-- TABLE: approvals
-- Purpose: Exception approval workflow management
-- Historical: Enhanced through migrations 002-006, 025 with schema fixes and notes
-- ============================================================================

CREATE TABLE approvals (
    id SERIAL PRIMARY KEY,
    trip_log_id INTEGER NOT NULL REFERENCES trip_logs(id) ON DELETE CASCADE,
    exception_type VARCHAR(50) NOT NULL,
    exception_description TEXT NOT NULL,
    severity VARCHAR(20) DEFAULT 'medium',
    status approval_status NOT NULL DEFAULT 'pending',
    requested_by INTEGER REFERENCES users(id),
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_by INTEGER REFERENCES users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE approvals IS 'Exception approval workflow for trip irregularities';
COMMENT ON COLUMN approvals.exception_type IS 'Type of exception requiring approval';
COMMENT ON COLUMN approvals.severity IS 'Severity level of the exception (low, medium, high, critical)';
COMMENT ON COLUMN approvals.approval_notes IS 'Notes from the reviewer about the approval decision';

-- ============================================================================
-- TABLE: scan_logs
-- Purpose: QR code scan audit trail
-- Historical: Enhanced through migrations 004-005 with location relationship fixes
-- ============================================================================

CREATE TABLE scan_logs (
    id SERIAL PRIMARY KEY,
    trip_log_id INTEGER REFERENCES trip_logs(id) ON DELETE CASCADE,
    scan_type scan_type NOT NULL,
    scanned_data TEXT NOT NULL,
    scanned_location_id INTEGER REFERENCES locations(id) ON DELETE SET NULL,
    scanned_truck_id INTEGER REFERENCES dump_trucks(id) ON DELETE SET NULL,
    scanned_by INTEGER REFERENCES users(id),
    scan_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    gps_latitude DECIMAL(10, 8),
    gps_longitude DECIMAL(11, 8),
    device_info JSONB,
    is_valid BOOLEAN DEFAULT true,
    validation_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE scan_logs IS 'Audit trail of all QR code scans in the system';
COMMENT ON COLUMN scan_logs.scan_type IS 'Type of QR code scan performed';
COMMENT ON COLUMN scan_logs.scanned_data IS 'Raw data from the QR code scan';
COMMENT ON COLUMN scan_logs.device_info IS 'Information about the scanning device';
COMMENT ON COLUMN scan_logs.is_valid IS 'Whether the scan was validated as legitimate';

-- ============================================================================
-- TABLE: driver_shifts
-- Purpose: Multi-driver shift management system
-- Historical: Major feature from migration 017, enhanced through migrations 022-064
-- ============================================================================

CREATE TABLE driver_shifts (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER NOT NULL REFERENCES drivers(id) ON DELETE CASCADE,
    shift_type shift_type NOT NULL DEFAULT 'day',
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status shift_status NOT NULL DEFAULT 'scheduled',
    recurrence_pattern recurrence_pattern DEFAULT 'single',
    display_type shift_type,
    shift_date DATE,
    handover_notes TEXT,
    completion_notes TEXT,
    cancellation_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure logical date and time relationships
    CONSTRAINT chk_shift_dates CHECK (start_date <= end_date),
    CONSTRAINT chk_shift_times CHECK (start_time != end_time)
);

COMMENT ON TABLE driver_shifts IS 'Multi-driver shift scheduling and management';
COMMENT ON COLUMN driver_shifts.shift_type IS 'Type of shift (day, night, custom)';
COMMENT ON COLUMN driver_shifts.recurrence_pattern IS 'How the shift repeats over time';
COMMENT ON COLUMN driver_shifts.display_type IS 'Display type for UI (may differ from shift_type)';
COMMENT ON COLUMN driver_shifts.shift_date IS 'Synchronized with start_date for backward compatibility';
COMMENT ON COLUMN driver_shifts.handover_notes IS 'Notes for shift handover process';

-- ============================================================================
-- TABLE: shift_handovers
-- Purpose: Shift transition management
-- Historical: Part of multi-driver system from migration 017
-- ============================================================================

CREATE TABLE shift_handovers (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    outgoing_shift_id INTEGER NOT NULL REFERENCES driver_shifts(id) ON DELETE CASCADE,
    incoming_shift_id INTEGER NOT NULL REFERENCES driver_shifts(id) ON DELETE CASCADE,
    active_trip_id INTEGER REFERENCES trip_logs(id),
    handover_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    truck_condition TEXT,
    fuel_level VARCHAR(20),
    notes TEXT,
    completed_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE shift_handovers IS 'Shift transition management and truck handover tracking';
COMMENT ON COLUMN shift_handovers.active_trip_id IS 'Trip in progress during handover (if any)';
COMMENT ON COLUMN shift_handovers.truck_condition IS 'Condition of truck at handover';
COMMENT ON COLUMN shift_handovers.fuel_level IS 'Fuel level at handover time';-- ============================================================================
-- SYSTEM MONITORING TABLES
-- Historical: Added through migrations 049-051, 055 for comprehensive system health
-- ============================================================================

-- ============================================================================
-- TABLE: system_logs
-- Purpose: System event logging and audit trail
-- ============================================================================

CREATE TABLE system_logs (
    id SERIAL PRIMARY KEY,
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    user_id INTEGER REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE system_logs IS 'System event logging and audit trail';
COMMENT ON COLUMN system_logs.log_type IS 'Type of system event (auth, error, info, etc.)';
COMMENT ON COLUMN system_logs.details IS 'Additional event details in JSON format';

-- ============================================================================
-- TABLE: system_tasks
-- Purpose: Automated task management and scheduling
-- ============================================================================

CREATE TABLE system_tasks (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    payload JSONB,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE system_tasks IS 'Automated task management and scheduling system';
COMMENT ON COLUMN system_tasks.type IS 'Type of automated task';
COMMENT ON COLUMN system_tasks.priority IS 'Task priority (low, medium, high, critical)';
COMMENT ON COLUMN system_tasks.payload IS 'Task-specific data in JSON format';

-- ============================================================================
-- TABLE: system_health_logs
-- Purpose: System health monitoring and metrics
-- ============================================================================

CREATE TABLE system_health_logs (
    id SERIAL PRIMARY KEY,
    module VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    issues JSONB,
    metrics JSONB,
    recommendations JSONB,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE system_health_logs IS 'System health monitoring and performance metrics';
COMMENT ON COLUMN system_health_logs.module IS 'System module being monitored';
COMMENT ON COLUMN system_health_logs.issues IS 'Identified issues in JSON format';
COMMENT ON COLUMN system_health_logs.metrics IS 'Performance metrics in JSON format';

-- ============================================================================
-- TABLE: automated_fix_logs
-- Purpose: Automated system fix tracking
-- Historical: Added in migration 050 for automated maintenance
-- ============================================================================

CREATE TABLE automated_fix_logs (
    id SERIAL PRIMARY KEY,
    module_name VARCHAR(50) NOT NULL,
    fix_type VARCHAR(50) NOT NULL DEFAULT 'automated_fix',
    success BOOLEAN NOT NULL DEFAULT false,
    message TEXT NOT NULL,
    details JSONB,
    affected_records INTEGER DEFAULT 0,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE automated_fix_logs IS 'Audit log for automated system health fixes';
COMMENT ON COLUMN automated_fix_logs.fix_type IS 'Type of automated fix performed';
COMMENT ON COLUMN automated_fix_logs.affected_records IS 'Number of records affected by the fix';

-- ============================================================================
-- TABLE: health_check_logs
-- Purpose: Health check results and monitoring
-- Historical: Added in migration 049 for system health tracking
-- ============================================================================

CREATE TABLE health_check_logs (
    id SERIAL PRIMARY KEY,
    check_name VARCHAR(100) NOT NULL,
    check_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    message TEXT,
    details JSONB,
    execution_time_ms INTEGER,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE health_check_logs IS 'System health check results and monitoring';
COMMENT ON COLUMN health_check_logs.check_type IS 'Type of health check performed';
COMMENT ON COLUMN health_check_logs.execution_time_ms IS 'Execution time in milliseconds';

-- ============================================================================
-- MIGRATION TRACKING TABLES
-- Historical: Migration management system
-- ============================================================================

-- ============================================================================
-- TABLE: migrations
-- Purpose: Simple migration tracking
-- ============================================================================

CREATE TABLE migrations (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    executed_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE migrations IS 'Simple migration tracking table';
COMMENT ON COLUMN migrations.filename IS 'Migration filename';

-- ============================================================================
-- TABLE: migration_log
-- Purpose: Detailed migration execution logs
-- ============================================================================

CREATE TABLE migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL,
    executed_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    description TEXT
);

COMMENT ON TABLE migration_log IS 'Migration execution logs';
COMMENT ON COLUMN migration_log.description IS 'Description of the migration';-- ============================================================================
-- INDEXES
-- ============================================================================
-- Performance optimization indexes based on query patterns and foreign keys

-- Primary table indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);

CREATE INDEX idx_dump_trucks_truck_number ON dump_trucks(truck_number);
CREATE INDEX idx_dump_trucks_license_plate ON dump_trucks(license_plate);
CREATE INDEX idx_dump_trucks_status ON dump_trucks(status);

CREATE INDEX idx_drivers_employee_id ON drivers(employee_id);
CREATE INDEX idx_drivers_license_number ON drivers(license_number);
CREATE INDEX idx_drivers_status ON drivers(status);
CREATE INDEX idx_drivers_license_expiry ON drivers(license_expiry);

CREATE INDEX idx_locations_location_code ON locations(location_code);
CREATE INDEX idx_locations_type ON locations(type);
CREATE INDEX idx_locations_is_active ON locations(is_active);

-- Assignment table indexes
CREATE INDEX idx_assignments_truck_id ON assignments(truck_id);
CREATE INDEX idx_assignments_driver_id ON assignments(driver_id);
CREATE INDEX idx_assignments_loading_location_id ON assignments(loading_location_id);
CREATE INDEX idx_assignments_unloading_location_id ON assignments(unloading_location_id);
CREATE INDEX idx_assignments_status ON assignments(status);
CREATE INDEX idx_assignments_assigned_date ON assignments(assigned_date);
CREATE INDEX idx_assignments_priority ON assignments(priority);
CREATE INDEX idx_assignments_is_adaptive ON assignments(is_adaptive);
CREATE INDEX idx_assignments_auto_created ON assignments(auto_created);
CREATE INDEX idx_assignments_is_shift_assignment ON assignments(is_shift_assignment);

-- Trip logs indexes for performance
CREATE INDEX idx_trip_logs_assignment_id ON trip_logs(assignment_id);
CREATE INDEX idx_trip_logs_status ON trip_logs(status);
CREATE INDEX idx_trip_logs_created_at ON trip_logs(created_at DESC);
CREATE INDEX idx_trip_logs_loading_start_time ON trip_logs(loading_start_time);
CREATE INDEX idx_trip_logs_is_exception ON trip_logs(is_exception);
CREATE INDEX idx_trip_logs_workflow_type ON trip_logs(workflow_type);
CREATE INDEX idx_trip_logs_performed_by_driver_id ON trip_logs(performed_by_driver_id);
CREATE INDEX idx_trip_logs_performed_by_shift_id ON trip_logs(performed_by_shift_id);

-- Approval workflow indexes
CREATE INDEX idx_approvals_trip_log_id ON approvals(trip_log_id);
CREATE INDEX idx_approvals_status ON approvals(status);
CREATE INDEX idx_approvals_exception_type ON approvals(exception_type);
CREATE INDEX idx_approvals_requested_by ON approvals(requested_by);
CREATE INDEX idx_approvals_reviewed_by ON approvals(reviewed_by);
CREATE INDEX idx_approvals_created_at ON approvals(created_at DESC);

-- Scan logs indexes
CREATE INDEX idx_scan_logs_trip_log_id ON scan_logs(trip_log_id);
CREATE INDEX idx_scan_logs_scan_type ON scan_logs(scan_type);
CREATE INDEX idx_scan_logs_scanned_location_id ON scan_logs(scanned_location_id);
CREATE INDEX idx_scan_logs_scanned_truck_id ON scan_logs(scanned_truck_id);
CREATE INDEX idx_scan_logs_scan_timestamp ON scan_logs(scan_timestamp DESC);
CREATE INDEX idx_scan_logs_is_valid ON scan_logs(is_valid);

-- Driver shifts indexes
CREATE INDEX idx_driver_shifts_truck_id ON driver_shifts(truck_id);
CREATE INDEX idx_driver_shifts_driver_id ON driver_shifts(driver_id);
CREATE INDEX idx_driver_shifts_status ON driver_shifts(status);
CREATE INDEX idx_driver_shifts_shift_type ON driver_shifts(shift_type);
CREATE INDEX idx_driver_shifts_start_date ON driver_shifts(start_date);
CREATE INDEX idx_driver_shifts_end_date ON driver_shifts(end_date);
CREATE INDEX idx_driver_shifts_recurrence_pattern ON driver_shifts(recurrence_pattern);
CREATE INDEX idx_driver_shifts_display_type ON driver_shifts(display_type);
CREATE INDEX idx_driver_shifts_shift_date ON driver_shifts(shift_date);

-- Shift handovers indexes
CREATE INDEX idx_shift_handovers_truck_id ON shift_handovers(truck_id);
CREATE INDEX idx_shift_handovers_outgoing_shift_id ON shift_handovers(outgoing_shift_id);
CREATE INDEX idx_shift_handovers_incoming_shift_id ON shift_handovers(incoming_shift_id);
CREATE INDEX idx_shift_handovers_handover_time ON shift_handovers(handover_time DESC);

-- System monitoring indexes
CREATE INDEX idx_system_logs_log_type ON system_logs(log_type);
CREATE INDEX idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at DESC);

CREATE INDEX idx_system_tasks_type ON system_tasks(type);
CREATE INDEX idx_system_tasks_status ON system_tasks(status);
CREATE INDEX idx_system_tasks_priority ON system_tasks(priority);
CREATE INDEX idx_system_tasks_scheduled_at ON system_tasks(scheduled_at);

CREATE INDEX idx_system_health_logs_module ON system_health_logs(module);
CREATE INDEX idx_system_health_logs_status ON system_health_logs(status);
CREATE INDEX idx_system_health_logs_checked_at ON system_health_logs(checked_at DESC);

CREATE INDEX idx_automated_fix_logs_module_name ON automated_fix_logs(module_name);
CREATE INDEX idx_automated_fix_logs_fix_type ON automated_fix_logs(fix_type);
CREATE INDEX idx_automated_fix_logs_success ON automated_fix_logs(success);
CREATE INDEX idx_automated_fix_logs_executed_at ON automated_fix_logs(executed_at DESC);
CREATE INDEX idx_automated_fix_logs_created_at ON automated_fix_logs(created_at DESC);

CREATE INDEX idx_health_check_logs_check_name ON health_check_logs(check_name);
CREATE INDEX idx_health_check_logs_check_type ON health_check_logs(check_type);
CREATE INDEX idx_health_check_logs_status ON health_check_logs(status);
CREATE INDEX idx_health_check_logs_checked_at ON health_check_logs(checked_at DESC);
CREATE INDEX idx_health_check_logs_created_at ON health_check_logs(created_at DESC);

CREATE INDEX idx_migration_log_migration_name ON migration_log(migration_name);
CREATE INDEX idx_migration_log_executed_at ON migration_log(executed_at DESC);

CREATE INDEX idx_migrations_filename ON migrations(filename);
CREATE INDEX idx_migrations_executed_at ON migrations(executed_at DESC);

-- GIN indexes for JSONB columns
CREATE INDEX idx_scan_logs_device_info_gin ON scan_logs USING GIN (device_info);
CREATE INDEX idx_system_logs_details_gin ON system_logs USING GIN (details);
CREATE INDEX idx_system_tasks_payload_gin ON system_tasks USING GIN (payload);
CREATE INDEX idx_system_health_logs_issues_gin ON system_health_logs USING GIN (issues);
CREATE INDEX idx_system_health_logs_metrics_gin ON system_health_logs USING GIN (metrics);
CREATE INDEX idx_automated_fix_logs_details_gin ON automated_fix_logs USING GIN (details);
CREATE INDEX idx_health_check_logs_details_gin ON health_check_logs USING GIN (details);