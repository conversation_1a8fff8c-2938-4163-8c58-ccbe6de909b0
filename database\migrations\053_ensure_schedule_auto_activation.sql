-- Migration: Ensure schedule_auto_activation function exists
-- Purpose: Create the schedule_auto_activation function if it doesn't exist

-- Check if the function exists and create it if it doesn't
DO $$
BEGIN
    -- Check if the function exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_proc WHERE proname = 'schedule_auto_activation'
    ) THEN
        -- Create the function
        EXECUTE '
        CREATE OR REPLACE FUNCTION schedule_auto_activation()
        RETURNS void AS $func$
        DECLARE
            shift_record RECORD;
            calculated_status TEXT;
            current_status TEXT;
            activated_count INTEGER := 0;
            scheduled_count INTEGER := 0;
            updated_count INTEGER := 0;
        BEGIN
            -- Process all non-cancelled and non-completed shifts
            FOR shift_record IN 
                SELECT id, status FROM driver_shifts 
                WHERE status NOT IN (''cancelled'', ''completed'')
            LOOP
                current_status := shift_record.status;
                
                -- Determine the correct status based on current time
                IF EXISTS (
                    SELECT 1 FROM driver_shifts
                    WHERE id = shift_record.id
                    AND start_date <= CURRENT_DATE
                    AND end_date >= CURRENT_DATE
                    AND (
                        (shift_type = ''day'' AND CURRENT_TIME BETWEEN start_time AND end_time) OR
                        (shift_type = ''night'' AND (CURRENT_TIME >= start_time OR CURRENT_TIME <= end_time))
                    )
                ) THEN
                    calculated_status := ''active'';
                ELSE
                    calculated_status := ''scheduled'';
                END IF;
                
                -- Only update if status has changed
                IF calculated_status != current_status THEN
                    -- Update the shift status
                    UPDATE driver_shifts 
                    SET status = calculated_status::shift_status, 
                        updated_at = CURRENT_TIMESTAMP 
                    WHERE id = shift_record.id;
                    
                    updated_count := updated_count + 1;
                    
                    -- Count by status type
                    IF calculated_status = ''active'' THEN
                        activated_count := activated_count + 1;
                    ELSIF calculated_status = ''scheduled'' THEN
                        scheduled_count := scheduled_count + 1;
                    END IF;
                END IF;
            END LOOP;
            
            -- Log the results if any updates were made
            IF updated_count > 0 THEN
                -- Check if system_logs table exists
                IF EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = ''public'' 
                    AND table_name = ''system_logs''
                ) THEN
                    INSERT INTO system_logs (
                        log_type, 
                        message, 
                        details
                    ) VALUES (
                        ''SHIFT_AUTO_ACTIVATION'',
                        ''Auto-activated shifts based on current time'',
                        jsonb_build_object(
                            ''updated_count'', updated_count,
                            ''activated_count'', activated_count,
                            ''scheduled_count'', scheduled_count,
                            ''timestamp'', CURRENT_TIMESTAMP
                        )
                    );
                END IF;
            END IF;
        END;
        $func$ LANGUAGE plpgsql;
        ';
        
        -- Add comment to the function
        EXECUTE '
        COMMENT ON FUNCTION schedule_auto_activation() IS 
        ''Auto-activates shifts based on current time. Does not automatically complete shifts.'';
        ';
        
        RAISE NOTICE 'Created schedule_auto_activation function';
    ELSE
        RAISE NOTICE 'schedule_auto_activation function already exists';
    END IF;
END;
$$;