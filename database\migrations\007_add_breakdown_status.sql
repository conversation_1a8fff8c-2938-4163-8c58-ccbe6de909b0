-- Migration: Add stopped status and management
-- Description: Adds 'stopped' status to trip_status enum for breakdown management
-- Version: 007
-- Date: 2024-12-19

-- Add 'stopped' to the trip_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'stopped' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'trip_status')
    ) THEN
        ALTER TYPE trip_status ADD VALUE 'stopped';
        RAISE NOTICE 'Added stopped status to trip_status enum';
    ELSE
        RAISE NOTICE 'Stopped status already exists in trip_status enum';
    END IF;
END $$;

-- Add stopped-related columns to trip_logs for better tracking
DO $$
BEGIN
    -- Add stopped_reported_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_reported_at'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_reported_at TIMESTAMP;
    END IF;

    -- Add stopped_reason column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_reason'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_reason TEXT;
    END IF;

    -- Add stopped_resolved_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_resolved_at'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_resolved_at TIMESTAMP;
    END IF;

    -- Add stopped_resolved_by column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_resolved_by'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_resolved_by INTEGER REFERENCES users(id);
    END IF;

    -- Add previous_status column to track status before stopped
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'previous_status'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN previous_status trip_status;
    END IF;
END $$;

-- Create indexes for stopped status queries (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trip_logs_stopped'
    ) THEN
        CREATE INDEX idx_trip_logs_stopped ON trip_logs(status) WHERE status = 'stopped';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trip_logs_stopped_date'
    ) THEN
        CREATE INDEX idx_trip_logs_stopped_date ON trip_logs(stopped_reported_at) WHERE stopped_reported_at IS NOT NULL;
    END IF;
END $$;

-- Add comments for documentation
COMMENT ON COLUMN trip_logs.stopped_reported_at IS 'Timestamp when the trip was stopped/reported as having issues';
COMMENT ON COLUMN trip_logs.stopped_reason IS 'Reason why the trip was stopped (mechanical issue, accident, etc.)';
COMMENT ON COLUMN trip_logs.stopped_resolved_at IS 'Timestamp when the stopped trip was resolved';
COMMENT ON COLUMN trip_logs.stopped_resolved_by IS 'User ID who resolved the stopped trip';

-- Update the view to include stopped information
DROP VIEW IF EXISTS v_trip_summary;
CREATE VIEW v_trip_summary AS
SELECT
    tl.id,
    a.assignment_code,
    a.assigned_date,
    dt.truck_number,
    d.full_name as driver_name,
    tl.trip_number,
    tl.status,
    tl.previous_status,
    tl.loading_start_time,
    tl.trip_completed_time,
    tl.total_duration_minutes,
    tl.loading_duration_minutes,
    tl.travel_duration_minutes,
    tl.unloading_duration_minutes,
    tl.is_exception,
    tl.exception_reason,
    tl.stopped_reported_at,
    tl.stopped_reason,
    tl.stopped_resolved_at,
    ll.name as loading_location,
    ul.name as unloading_location,
    COALESCE(al.name, ll.name) as actual_loading_location,
    COALESCE(aul.name, ul.name) as actual_unloading_location
FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
ORDER BY tl.created_at DESC;

-- Migration completed successfully
