# System Health Fix Operations Documentation

This document provides detailed explanations of what each System Health Monitor fix operation does and when to use them.

## 1. Shift Management Fix (`POST /api/system-health/fix-shifts`)

### What it fixes:
- **Shift Status Synchronization**: Corrects shift statuses based on current date and time
- **Automatic Activation**: Moves scheduled shifts to active when their start time arrives
- **Automatic Completion**: Moves active shifts to completed when their end time passes
- **Time-based Validation**: Ensures shift statuses match real-world time constraints

### Specific Operations:
1. **Schedule Auto-Activation**: Calls `schedule_auto_activation()` database function
2. **Status Correction**: Updates shifts with incorrect status based on current time
3. **Overnight Logic**: Handles night shifts that span midnight correctly
4. **Date Range Validation**: Ensures multi-day shifts have correct status progression

### When to use:
- Shifts appear "stuck" in scheduled status despite start time passing
- Active shifts don't automatically complete at end time
- After system downtime or maintenance
- When shift status doesn't match current time

### Expected Results:
- Active shifts: Currently running shifts within their time window
- Scheduled shifts: Future shifts waiting to start
- Completed shifts: Past shifts that have ended
- Total count of all shifts processed

---

## 2. Assignment Management Fix (`POST /api/system-health/fix-assignments`)

### What it fixes:
- **Driver-Assignment Synchronization**: Ensures assignments reflect current active shifts
- **Orphaned Assignments**: Updates assignments without valid drivers
- **Cross-System Consistency**: Synchronizes assignment data with shift management
- **Driver Availability**: Matches truck assignments with available drivers

### Specific Operations:
1. **Active Shift Detection**: Identifies currently active driver shifts
2. **Assignment Updates**: Updates truck assignments to match active drivers
3. **Null Driver Cleanup**: Assigns drivers to trucks that lack driver assignments
4. **Conflict Resolution**: Resolves cases where assignments don't match shifts

### When to use:
- Assignments show wrong drivers for trucks
- Trucks appear unassigned despite active shifts
- After shift transitions or driver changes
- When assignment data is inconsistent with shift data

### Expected Results:
- Updated assignments count: Number of assignments synchronized
- Driver-truck matching: Correct driver assigned to each truck
- Consistency: Assignment data matches active shift data

---

## 3. Trip Monitoring Fix (`POST /api/system-health/fix-trips`)

### What it fixes:
- **"No Active Shift" Synchronization Issues**: Resolves cases where Trip Monitoring shows "No Active Shift" despite active shifts existing in Shift Management
- **Driver-Shift Display Synchronization**: Ensures Trip Monitoring displays correct driver shift status
- **Cross-System Consistency**: Synchronizes Trip Monitoring with Shift Management data
- **Invalid Trip Statuses**: Corrects trips with non-standard status values (legacy functionality)

### Specific Operations:
1. **"No Active Shift" Detection**: Identifies assignments showing "⚠️ No Active Shift" in Trip Monitoring
2. **Active Shift Cross-Reference**: Searches Shift Management for actual active shifts for affected trucks
3. **Enhanced Time Logic**: Uses improved night shift logic that properly handles overnight shifts:
   - Day shifts: Simple time range check (start_time to end_time)
   - Night shifts: Dual condition logic for midnight-crossing shifts
   - Proper date range validation (start_date to end_date)
4. **Assignment Synchronization**: Updates assignments to match active shift drivers
5. **Status Validation**: Checks trips against valid status list:
   - `assigned` - Trip assigned to driver
   - `loading_start` - Loading phase begun
   - `loading_end` - Loading phase completed
   - `unloading_start` - Unloading phase begun
   - `unloading_end` - Unloading phase completed
   - `trip_completed` - Trip fully completed
   - `cancelled` - Trip cancelled
   - `stopped` - Trip stopped due to issues
6. **Invalid Status Correction**: Updates trips with invalid statuses to `assigned`

### When to use:
- Trip Monitoring shows "No Active Shift" for drivers who have active shifts
- Driver shift status is inconsistent between Shift Management and Trip Monitoring
- After shift transitions or driver changes
- When Trip Monitoring doesn't reflect current shift assignments
- Trips show invalid or corrupted status values

### Expected Results:
- Fixed shift sync issues: Count of "No Active Shift" cases resolved
- Driver consistency: Trip Monitoring shows correct active shift information
- Fixed invalid statuses: Count of trips with corrected status values
- Cross-system alignment: Trip Monitoring matches Shift Management data

### Example Fix Scenario:
**Before Fix:**
- Shift Management: Maria Garcia (DR-002) - Active Night Shift on DT-100
- Trip Monitoring: "DT-100 • TRK-001 ⚠️ Maria Garcia(DR-002)⚠️ No Active Shift"

**After Fix:**
- Trip Monitoring: "DT-100 • TRK-001 ✅ Maria Garcia(DR-002) ✅ Night Shift Active"

---

## 4. Database Health Fix (`POST /api/system-health/fix-database`)

### What it fixes:
- **Performance Optimization**: Runs VACUUM and ANALYZE on key tables
- **Orphaned Records**: Removes records that reference non-existent data
- **Constraint Violations**: Fixes foreign key and constraint issues
- **Index Maintenance**: Updates database statistics for query optimization

### Specific Operations:
1. **VACUUM Operations**: Reclaims storage space on key tables:
   - `trip_logs` - Trip history and status data
   - `assignments` - Truck-driver assignments
   - `driver_shifts` - Shift scheduling data
   - `scan_logs` - QR code scan history

2. **Orphaned Data Cleanup**:
   - Removes scan_logs without valid trip_log_id references
   - Cleans up assignments with invalid driver references

3. **Constraint Fixes**:
   - Updates assignments with inactive drivers to NULL
   - Fixes foreign key violations

4. **Statistics Update**:
   - Runs ANALYZE to update query planner statistics
   - Improves database performance for future queries

### When to use:
- Database performance is slow
- After large data operations or imports
- When foreign key errors occur
- During regular maintenance (weekly/monthly)

### Expected Results:
- Vacuum operations: Count of tables optimized
- Orphaned records cleaned: Number of invalid records removed
- Constraint violations fixed: Number of constraint issues resolved
- Performance improvement: Faster query execution

---

## Usage Guidelines

### Frequency Recommendations:
- **Shift Fix**: Run when shift issues are reported (as needed)
- **Assignment Fix**: Run after shift changes or driver updates (daily)
- **Trip Fix**: Run when trip workflow issues occur (as needed)
- **Database Fix**: Run during maintenance windows (weekly)

### Safety Considerations:
- All operations use database transactions for safety
- Failed operations are automatically rolled back
- Operations are logged for audit purposes
- No data is permanently deleted without backup

### Monitoring:
- Each operation provides detailed success/failure information
- Affected record counts are reported
- Error messages include specific details for troubleshooting
- All operations are logged to system_logs table

### Troubleshooting:
If a fix operation fails:
1. Check the error message for specific details
2. Verify database connectivity and permissions
3. Check for conflicting operations or locks
4. Review system logs for additional context
5. Contact system administrator if issues persist
