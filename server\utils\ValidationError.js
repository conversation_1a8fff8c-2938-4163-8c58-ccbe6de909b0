/**
 * Custom error class for business rule validation errors
 * These should return 400 Bad Request instead of 500 Internal Server Error
 * 
 * @class ValidationError
 * @extends Error
 * @example
 * // Basic usage
 * throw new ValidationError('Invalid truck assignment');
 * 
 * @example
 * // With details for structured error responses
 * throw new ValidationError('Trip validation failed', {
 *   field: 'truck_id',
 *   code: 'TRUCK_NOT_FOUND',
 *   context: { truck_id: 123 }
 * });
 */
class ValidationError extends Error {
  /**
   * Creates a new ValidationError instance
   * @param {string} message - The error message describing the validation failure
   * @param {Object} [details={}] - Additional error details for structured responses
   * @param {string} [details.field] - The field that failed validation
   * @param {string} [details.code] - Error code for programmatic handling
   * @param {Object} [details.context] - Additional context data
   * @param {string} [details.suggestion] - Suggested action to resolve the error
   */
  constructor(message, details = {}) {
    // Input validation
    if (typeof message !== 'string' || message.trim().length === 0) {
      throw new TypeError('ValidationError message must be a non-empty string');
    }
    
    if (details !== null && typeof details !== 'object') {
      throw new TypeError('ValidationError details must be an object or null');
    }

    super(message);
    this.name = 'ValidationError';
    this.isValidationError = true;
    this.details = details || {};
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ValidationError);
    }
  }

  /**
   * Creates a ValidationError for field-specific validation failures
   * @param {string} field - The field that failed validation
   * @param {string} message - The validation error message
   * @param {*} value - The invalid value that was provided
   * @param {string} [suggestion] - Suggested fix for the validation error
   * @returns {ValidationError} A new ValidationError instance
   * @static
   */
  static forField(field, message, value, suggestion = null) {
    const details = {
      field,
      code: `INVALID_${field.toUpperCase()}`,
      context: { [field]: value }
    };
    
    if (suggestion) {
      details.suggestion = suggestion;
    }

    return new ValidationError(`${field}: ${message}`, details);
  }

  /**
   * Creates a ValidationError for workflow violations
   * @param {string} currentState - The current workflow state
   * @param {string} attemptedAction - The action that was attempted
   * @param {string} [expectedState] - The expected state for the action
   * @returns {ValidationError} A new ValidationError instance
   * @static
   */
  static forWorkflow(currentState, attemptedAction, expectedState = null) {
    const message = expectedState 
      ? `Cannot ${attemptedAction} from ${currentState}. Expected state: ${expectedState}`
      : `Cannot ${attemptedAction} from current state: ${currentState}`;

    return new ValidationError(message, {
      code: 'WORKFLOW_VIOLATION',
      field: 'workflow_state',
      context: {
        current_state: currentState,
        attempted_action: attemptedAction,
        expected_state: expectedState
      },
      suggestion: expectedState ? `Ensure the workflow is in ${expectedState} state before attempting this action` : null
    });
  }

  /**
   * Creates a ValidationError for business rule violations
   * @param {string} rule - The business rule that was violated
   * @param {Object} context - Context information about the violation
   * @returns {ValidationError} A new ValidationError instance
   * @static
   */
  static forBusinessRule(rule, context = {}) {
    return new ValidationError(`Business rule violation: ${rule}`, {
      code: 'BUSINESS_RULE_VIOLATION',
      field: 'business_rule',
      context,
      suggestion: 'Please review the business rules and ensure compliance'
    });
  }

  /**
   * Converts the error to a structured JSON response suitable for API responses
   * @returns {Object} Structured error object
   */
  toJSON() {
    return {
      error: this.name,
      message: this.message,
      details: this.details,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Checks if an error is a ValidationError instance
   * @param {*} error - The error to check
   * @returns {boolean} True if the error is a ValidationError
   * @static
   */
  static isValidationError(error) {
    return error instanceof ValidationError || error?.isValidationError === true;
  }
}

module.exports = ValidationError;