/**
 * Manual Shift Management Service
 * Purpose: Provide manual control over shift status transitions
 * Features: Manual completion, cancellation, and status refresh
 */

const { getClient } = require('../config/database');

class ManualShiftManagementService {
  constructor() {
    this.logger = console;
    this.tableCache = new Map(); // Cache table existence checks
    this.ensureSystemLogsTable();
  }

  /**
   * Check if a table exists (with caching)
   * @param {Object} client - Database client
   * @param {string} tableName - Name of the table to check
   * @returns {Promise<boolean>} True if table exists
   */
  async tableExists(client, tableName) {
    // Check cache first
    if (this.tableCache.has(tableName)) {
      return this.tableCache.get(tableName);
    }

    try {
      const result = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        ) as table_exists
      `, [tableName]);

      const exists = result.rows[0].table_exists;
      this.tableCache.set(tableName, exists);
      return exists;
    } catch (error) {
      this.logger.error(`Error checking table existence for ${tableName}:`, error);
      return false;
    }
  }

  /**
   * Ensure the system_logs table exists
   */
  async ensureSystemLogsTable() {
    let client;

    try {
      client = await getClient();

      // Check if system_logs table exists using cached method
      if (!(await this.tableExists(client, 'system_logs'))) {
        this.logger.info('Creating system_logs table...');

        await client.query(`
          CREATE TABLE IF NOT EXISTS system_logs (
            id SERIAL PRIMARY KEY,
            log_type VARCHAR(50) NOT NULL,
            message TEXT NOT NULL,
            details JSONB,
            user_id INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          )
        `);

        this.logger.info('system_logs table created successfully');
      }
    } catch (error) {
      this.logger.error('Error ensuring system_logs table exists:', error);
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Get all active shifts for manual management
   * @returns {Promise<Array>} List of active shifts
   */
  async getActiveShifts() {
    let client;

    try {
      client = await getClient();

      // Check if driver_shifts table exists using cached method
      if (!(await this.tableExists(client, 'driver_shifts'))) {
        return [];
      }

      // Check if drivers and dump_trucks tables exist using cached method
      const driversExists = await this.tableExists(client, 'drivers');
      const trucksExists = await this.tableExists(client, 'dump_trucks');

      // If either table doesn't exist, use a simpler query with completion logic
      if (!driversExists || !trucksExists) {
        const result = await client.query(`
          SELECT 
            id,
            driver_id,
            truck_id,
            shift_type,
            status,
            start_date,
            end_date,
            start_time,
            end_time,
            created_at,
            updated_at,
            'Unknown' as driver_name,
            'Unknown' as truck_number,
            -- Calculate if shift is eligible for completion (end date/time matches current date/time)
            CASE 
              WHEN end_date = CURRENT_DATE AND 
                   EXTRACT(HOUR FROM end_time) = EXTRACT(HOUR FROM CURRENT_TIME) AND
                   EXTRACT(MINUTE FROM end_time) = EXTRACT(MINUTE FROM CURRENT_TIME)
              THEN true
              ELSE false
            END as can_complete,
            -- Calculate time until completion eligibility
            CASE 
              WHEN end_date = CURRENT_DATE THEN
                EXTRACT(EPOCH FROM (end_time - CURRENT_TIME)) / 60
              WHEN end_date > CURRENT_DATE THEN
                EXTRACT(EPOCH FROM ((end_date + end_time) - (CURRENT_DATE + CURRENT_TIME))) / 60
              ELSE -1 -- Past due
            END as minutes_until_completion,
            -- Enhanced completion status
            CASE 
              WHEN end_date < CURRENT_DATE OR 
                   (end_date = CURRENT_DATE AND end_time <= CURRENT_TIME)
              THEN 'ready_for_completion'
              WHEN end_date = CURRENT_DATE
              THEN 'completing_today'
              ELSE 'future_shift'
            END as completion_status
          FROM 
            driver_shifts
          WHERE 
            status = 'active'
          ORDER BY 
            end_date, end_time
        `);

        return result.rows;
      }

      // All tables exist, use the simplified query with completion eligibility
      const result = await client.query(`
        SELECT 
          ds.id,
          ds.driver_id,
          ds.truck_id,
          ds.shift_type,
          ds.status,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time,
          ds.created_at,
          ds.updated_at,
          d.full_name as driver_name,
          dt.truck_number,
          -- Simple completion eligibility: end date/time has passed
          CASE 
            WHEN ds.end_date < CURRENT_DATE OR 
                 (ds.end_date = CURRENT_DATE AND ds.end_time <= CURRENT_TIME)
            THEN true
            ELSE false
          END as can_complete,
          -- Simple time display
          CASE 
            WHEN ds.end_date < CURRENT_DATE OR 
                 (ds.end_date = CURRENT_DATE AND ds.end_time <= CURRENT_TIME)
            THEN 'Ready Now'
            WHEN ds.end_date = CURRENT_DATE
            THEN 'Today'
            ELSE 'Future'
          END as time_until_completion,
          -- Enhanced completion status
          CASE 
            WHEN ds.end_date < CURRENT_DATE OR 
                 (ds.end_date = CURRENT_DATE AND ds.end_time <= CURRENT_TIME)
            THEN 'ready_for_completion'
            WHEN ds.end_date = CURRENT_DATE
            THEN 'completing_today'
            ELSE 'future_shift'
          END as completion_status
        FROM 
          driver_shifts ds
          LEFT JOIN drivers d ON ds.driver_id = d.id
          LEFT JOIN dump_trucks dt ON ds.truck_id = dt.id
        WHERE 
          ds.status = 'active'
        ORDER BY 
          ds.end_date, ds.end_time
      `);

      return result.rows;
    } catch (error) {
      this.logger.error('Error getting active shifts:', error);
      return [];
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Get all scheduled shifts for manual management
   * @returns {Promise<Array>} List of scheduled shifts
   */
  async getScheduledShifts() {
    let client;

    try {
      client = await getClient();

      // Check if driver_shifts table exists using cached method
      if (!(await this.tableExists(client, 'driver_shifts'))) {
        return [];
      }

      // Check if drivers and dump_trucks tables exist using cached method
      const driversExists = await this.tableExists(client, 'drivers');
      const trucksExists = await this.tableExists(client, 'dump_trucks');

      // If either table doesn't exist, use a simpler query with completion logic
      if (!driversExists || !trucksExists) {
        const result = await client.query(`
          SELECT 
            id,
            driver_id,
            truck_id,
            shift_type,
            status,
            start_date,
            end_date,
            start_time,
            end_time,
            created_at,
            updated_at,
            'Unknown' as driver_name,
            'Unknown' as truck_number,
            -- Calculate if shift is eligible for completion (end date/time matches current date/time)
            CASE 
              WHEN end_date = CURRENT_DATE AND 
                   EXTRACT(HOUR FROM end_time) = EXTRACT(HOUR FROM CURRENT_TIME) AND
                   EXTRACT(MINUTE FROM end_time) = EXTRACT(MINUTE FROM CURRENT_TIME)
              THEN true
              ELSE false
            END as can_complete,
            -- Calculate time until completion eligibility
            CASE 
              WHEN end_date = CURRENT_DATE THEN
                EXTRACT(EPOCH FROM (end_time - CURRENT_TIME)) / 60
              WHEN end_date > CURRENT_DATE THEN
                EXTRACT(EPOCH FROM ((end_date + end_time) - (CURRENT_DATE + CURRENT_TIME))) / 60
              ELSE -1 -- Past due
            END as minutes_until_completion,
            -- Enhanced completion status
            CASE 
              WHEN end_date < CURRENT_DATE OR 
                   (end_date = CURRENT_DATE AND end_time <= CURRENT_TIME)
              THEN 'ready_for_completion'
              WHEN end_date = CURRENT_DATE
              THEN 'completing_today'
              ELSE 'future_shift'
            END as completion_status
          FROM 
            driver_shifts
          WHERE 
            status = 'scheduled'
          ORDER BY 
            start_date, start_time
        `);

        return result.rows;
      }

      // All tables exist, use the simplified query with completion eligibility
      const result = await client.query(`
        SELECT 
          ds.id,
          ds.driver_id,
          ds.truck_id,
          ds.shift_type,
          ds.status,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time,
          ds.created_at,
          ds.updated_at,
          d.full_name as driver_name,
          dt.truck_number,
          -- Simple completion eligibility: end date/time has passed
          CASE 
            WHEN ds.end_date < CURRENT_DATE OR 
                 (ds.end_date = CURRENT_DATE AND ds.end_time <= CURRENT_TIME)
            THEN true
            ELSE false
          END as can_complete,
          -- Simple time display
          CASE 
            WHEN ds.end_date < CURRENT_DATE OR 
                 (ds.end_date = CURRENT_DATE AND ds.end_time <= CURRENT_TIME)
            THEN 'Ready Now'
            WHEN ds.end_date = CURRENT_DATE
            THEN 'Today'
            ELSE 'Future'
          END as time_until_completion,
          -- Enhanced completion status
          CASE 
            WHEN ds.end_date < CURRENT_DATE OR 
                 (ds.end_date = CURRENT_DATE AND ds.end_time <= CURRENT_TIME)
            THEN 'ready_for_completion'
            WHEN ds.end_date = CURRENT_DATE
            THEN 'completing_today'
            ELSE 'future_shift'
          END as completion_status
        FROM 
          driver_shifts ds
          LEFT JOIN drivers d ON ds.driver_id = d.id
          LEFT JOIN dump_trucks dt ON ds.truck_id = dt.id
        WHERE 
          ds.status = 'scheduled'
        ORDER BY 
          ds.start_date, ds.start_time
      `);

      return result.rows;
    } catch (error) {
      this.logger.error('Error getting scheduled shifts:', error);
      return [];
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Manually complete a shift
   * @param {number} shiftId - ID of the shift to complete
   * @param {string} userId - ID of the user performing the action
   * @param {string} notes - Optional completion notes
   * @returns {Promise<Object>} Result of the operation
   */
  async completeShift(shiftId, userId, notes = '') {
    let client;

    try {
      client = await getClient();

      // Begin transaction
      await client.query('BEGIN');

      // Get current shift status
      const currentStatus = await client.query(`
        SELECT status FROM driver_shifts WHERE id = $1
      `, [shiftId]);

      if (currentStatus.rows.length === 0) {
        throw new Error(`Shift with ID ${shiftId} not found`);
      }

      const status = currentStatus.rows[0].status;

      // Only active and scheduled shifts can be completed (when ready)
      if (status !== 'active' && status !== 'scheduled') {
        throw new Error(`Cannot complete shift with status '${status}'. Only active or scheduled shifts can be completed.`);
      }

      // Update shift status to completed
      const result = await client.query(`
        UPDATE driver_shifts
        SET 
          status = 'completed',
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING id, status, updated_at
      `, [shiftId]);

      // Check if completion_notes column exists
      const columnCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'driver_shifts'
          AND column_name = 'completion_notes'
        ) as column_exists
      `);

      // If completion_notes column exists, update it
      if (columnCheck.rows[0].column_exists) {
        await client.query(`
          UPDATE driver_shifts
          SET completion_notes = $2
          WHERE id = $1
        `, [shiftId, notes]);
      }

      // Log the manual completion
      try {
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details,
            user_id
          ) VALUES (
            'SHIFT_MANUAL_COMPLETION',
            'Shift manually completed',
            $1,
            $2
          )
        `, [
          JSON.stringify({
            shift_id: shiftId,
            previous_status: status,
            new_status: 'completed',
            notes: notes
          }),
          userId
        ]);
      } catch (error) {
        // Ignore system_logs errors
        this.logger.warn('Error logging shift completion:', error);
      }

      // Commit transaction
      await client.query('COMMIT');

      return {
        success: true,
        message: 'Shift completed successfully',
        shift: result.rows[0]
      };
    } catch (error) {
      if (client) {
        await client.query('ROLLBACK');
      }
      this.logger.error('Error completing shift:', error);
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Manually cancel a shift
   * @param {number} shiftId - ID of the shift to cancel
   * @param {string} userId - ID of the user performing the action
   * @param {string} reason - Reason for cancellation
   * @returns {Promise<Object>} Result of the operation
   */
  async cancelShift(shiftId, userId, reason = '') {
    let client;

    try {
      client = await getClient();

      // Begin transaction
      await client.query('BEGIN');

      // Get current shift status
      const currentStatus = await client.query(`
        SELECT status FROM driver_shifts WHERE id = $1
      `, [shiftId]);

      if (currentStatus.rows.length === 0) {
        throw new Error(`Shift with ID ${shiftId} not found`);
      }

      const status = currentStatus.rows[0].status;

      // Only active or scheduled shifts can be cancelled
      if (status !== 'active' && status !== 'scheduled') {
        throw new Error(`Cannot cancel shift with status '${status}'. Only active or scheduled shifts can be cancelled.`);
      }

      // Update shift status to cancelled
      const result = await client.query(`
        UPDATE driver_shifts
        SET 
          status = 'cancelled',
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING id, status, updated_at
      `, [shiftId]);

      // Check if cancellation_reason column exists
      const columnCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'driver_shifts'
          AND column_name = 'cancellation_reason'
        ) as column_exists
      `);

      // If cancellation_reason column exists, update it
      if (columnCheck.rows[0].column_exists) {
        await client.query(`
          UPDATE driver_shifts
          SET cancellation_reason = $2
          WHERE id = $1
        `, [shiftId, reason]);
      }

      // Log the manual cancellation
      try {
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details,
            user_id
          ) VALUES (
            'SHIFT_MANUAL_CANCELLATION',
            'Shift manually cancelled',
            $1,
            $2
          )
        `, [
          JSON.stringify({
            shift_id: shiftId,
            previous_status: status,
            new_status: 'cancelled',
            reason: reason
          }),
          userId
        ]);
      } catch (error) {
        // Ignore system_logs errors
        this.logger.warn('Error logging shift cancellation:', error);
      }

      // Commit transaction
      await client.query('COMMIT');

      return {
        success: true,
        message: 'Shift cancelled successfully',
        shift: result.rows[0]
      };
    } catch (error) {
      if (client) {
        await client.query('ROLLBACK');
      }
      this.logger.error('Error cancelling shift:', error);
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Force refresh shift statuses (scheduled/active only)
   * @param {string} userId - ID of the user performing the action
   * @returns {Promise<Object>} Result of the operation with statistics
   */
  async forceRefreshStatuses(userId) {
    let client;

    try {
      client = await getClient();

      // Begin transaction
      await client.query('BEGIN');

      // Check if schedule_auto_activation function exists
      const functionCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM pg_proc 
          WHERE proname = 'schedule_auto_activation'
        ) as function_exists
      `);

      // If function exists, execute it
      if (functionCheck.rows[0].function_exists) {
        await client.query(`SELECT schedule_auto_activation()`);
      } else {
        // Function doesn't exist, return error
        throw new Error('schedule_auto_activation function does not exist');
      }

      // Get updated statistics
      const stats = await client.query(`
        SELECT
          COUNT(*) FILTER (WHERE status = 'active') as active_count,
          COUNT(*) FILTER (WHERE status = 'scheduled') as scheduled_count,
          COUNT(*) FILTER (WHERE status = 'completed') as completed_count,
          COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_count,
          COUNT(*) as total_count
        FROM driver_shifts
      `);

      // Log the manual refresh
      try {
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details,
            user_id
          ) VALUES (
            'SHIFT_MANUAL_REFRESH',
            'Shift statuses manually refreshed',
            $1,
            $2
          )
        `, [
          JSON.stringify({
            timestamp: new Date().toISOString(),
            statistics: stats.rows[0]
          }),
          userId
        ]);
      } catch (error) {
        // Ignore system_logs errors
        this.logger.warn('Error logging shift refresh:', error);
      }

      // Commit transaction
      await client.query('COMMIT');

      return {
        success: true,
        message: 'Shift statuses refreshed successfully',
        statistics: stats.rows[0]
      };
    } catch (error) {
      if (client) {
        await client.query('ROLLBACK');
      }
      this.logger.error('Error refreshing shift statuses:', error);
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Get shift status summary
   * @returns {Promise<Object>} Summary of shift statuses
   */
  async getShiftStatusSummary() {
    let client;

    try {
      client = await getClient();

      // Check if driver_shifts table exists using cached method
      if (!(await this.tableExists(client, 'driver_shifts'))) {
        return {
          active_count: 0,
          scheduled_count: 0,
          completed_count: 0,
          cancelled_count: 0,
          total_count: 0
        };
      }

      const result = await client.query(`
        SELECT
          COUNT(*) FILTER (WHERE status = 'active') as active_count,
          COUNT(*) FILTER (WHERE status = 'scheduled') as scheduled_count,
          COUNT(*) FILTER (WHERE status = 'completed') as completed_count,
          COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_count,
          COUNT(*) as total_count
        FROM driver_shifts
      `);

      return result.rows[0];
    } catch (error) {
      this.logger.error('Error getting shift status summary:', error);
      return {
        active_count: 0,
        scheduled_count: 0,
        completed_count: 0,
        cancelled_count: 0,
        total_count: 0
      };
    } finally {
      if (client) {
        client.release();
      }
    }
  }
}

// Export singleton instance
const manualShiftManagementService = new ManualShiftManagementService();
module.exports = manualShiftManagementService;