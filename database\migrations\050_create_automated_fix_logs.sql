-- Migration 050: Create automated fix logs table for system health monitoring
-- This table stores audit logs of all automated fix operations

-- Create automated_fix_logs table
CREATE TABLE IF NOT EXISTS automated_fix_logs (
    id SERIAL PRIMARY KEY,
    module_name VARCHAR(50) NOT NULL,
    fix_type VARCHAR(50) NOT NULL DEFAULT 'automated_fix',
    success BOOLEAN NOT NULL DEFAULT false,
    message TEXT NOT NULL,
    details JSONB,
    affected_records INTEGER DEFAULT 0,
    executed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_automated_fix_logs_module_name ON automated_fix_logs(module_name);
CREATE INDEX IF NOT EXISTS idx_automated_fix_logs_executed_at ON automated_fix_logs(executed_at DESC);
CREATE INDEX IF NOT EXISTS idx_automated_fix_logs_success ON automated_fix_logs(success);

-- Add comments for documentation
COMMENT ON TABLE automated_fix_logs IS 'Audit log for automated system health fixes';
COMMENT ON COLUMN automated_fix_logs.module_name IS 'Module that was fixed (shift_management, assignment_management, trip_monitoring)';
COMMENT ON COLUMN automated_fix_logs.fix_type IS 'Type of fix operation performed';
COMMENT ON COLUMN automated_fix_logs.success IS 'Whether the fix operation completed successfully';
COMMENT ON COLUMN automated_fix_logs.message IS 'Human-readable summary of the fix operation';
COMMENT ON COLUMN automated_fix_logs.details IS 'Detailed information about the fix operation in JSON format';
COMMENT ON COLUMN automated_fix_logs.affected_records IS 'Number of database records affected by the fix';
COMMENT ON COLUMN automated_fix_logs.executed_at IS 'When the fix operation was executed';

-- Insert initial test record to verify table structure
INSERT INTO automated_fix_logs (
    module_name,
    fix_type,
    success,
    message,
    details,
    affected_records
) VALUES (
    'system_initialization',
    'table_creation',
    true,
    'Created automated_fix_logs table for system health monitoring',
    '{"migration": "050_create_automated_fix_logs.sql", "tables_created": ["automated_fix_logs"]}',
    1
);

-- Grant permissions to application user (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'hauling_app_user') THEN
        GRANT SELECT, INSERT, UPDATE ON automated_fix_logs TO hauling_app_user;
        GRANT USAGE, SELECT ON SEQUENCE automated_fix_logs_id_seq TO hauling_app_user;
    END IF;
END $$;