-- Migration: Improved debug_shift_status function
-- Created: 2025-07-18

-- Helper functions for better modularity
CREATE OR REPLACE FUNCTION is_overnight_shift(start_time TIME, end_time TIME)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN end_time < start_time;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION calculate_shift_end_timestamp(
    end_date DATE,
    end_time TIME,
    is_overnight BOOLEAN
) RETURNS TIMESTAMP AS $$
BEGIN
    IF is_overnight THEN
        RETURN (end_date + INTERVAL '1 day')::DATE + end_time;
    ELSE
        RETURN end_date::DATE + end_time;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION calculate_shift_status(
    within_date_range BOOLEAN,
    within_time_window BOOLEAN
) RETURNS TEXT AS $$
BEGIN
    CASE 
        WHEN within_date_range AND within_time_window THEN
            RETURN 'active';
        WHEN within_date_range AND NOT within_time_window THEN
            RETURN 'scheduled';
        ELSE
            RETURN 'scheduled';
    END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Drop and recreate the main debug function
DROP FUNCTION IF EXISTS debug_shift_status(INTEGER, TIMESTAMP WITH TIME ZONE) CASCADE;

CREATE OR REPLACE FUNCTION debug_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE (
    shift_id INTEGER,
    shift_start_date DATE,
    shift_end_date DATE,
    shift_start_time TIME,
    shift_end_time TIME,
    current_date_val DATE,
    current_time_val TIME,
    is_overnight BOOLEAN,
    is_within_date_range BOOLEAN,
    is_within_time_window BOOLEAN,
    shift_end_datetime TIMESTAMP,
    is_past_completion BOOLEAN,
    calculated_status TEXT
) AS $$
DECLARE
    shift_record RECORD;
    current_date_val DATE;
    current_time_val TIME;
    overnight_shift BOOLEAN;
    within_date_range BOOLEAN;
    within_time_window BOOLEAN;
    past_completion BOOLEAN;
    status_result TEXT;
    shift_end_timestamp TIMESTAMP;
BEGIN
    -- Input validation
    IF p_shift_id IS NULL OR p_shift_id <= 0 THEN
        RAISE EXCEPTION 'Invalid shift_id: %', p_shift_id;
    END IF;
    
    -- Get shift details
    SELECT * INTO shift_record
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RAISE NOTICE 'Shift with ID % not found', p_shift_id;
        RETURN;
    END IF;

    -- Calculate derived values
    current_date_val := p_reference_timestamp::DATE;
    current_time_val := p_reference_timestamp::TIME;
    overnight_shift := is_overnight_shift(shift_record.start_time, shift_record.end_time);
    within_date_range := current_date_val BETWEEN shift_record.start_date AND shift_record.end_date;
    
    -- Time window validation
    IF overnight_shift THEN
        within_time_window := (current_time_val >= shift_record.start_time OR current_time_val <= shift_record.end_time);
    ELSE
        within_time_window := (current_time_val BETWEEN shift_record.start_time AND shift_record.end_time);
    END IF;

    -- Calculate completion status
    shift_end_timestamp := calculate_shift_end_timestamp(shift_record.end_date, shift_record.end_time, overnight_shift);
    past_completion := p_reference_timestamp > shift_end_timestamp;
    
    -- Determine status
    status_result := calculate_shift_status(within_date_range, within_time_window);

    -- Return results
    RETURN QUERY SELECT
        shift_record.id::INTEGER,
        shift_record.start_date::DATE,
        shift_record.end_date::DATE,
        shift_record.start_time::TIME,
        shift_record.end_time::TIME,
        current_date_val::DATE,
        current_time_val::TIME,
        overnight_shift::BOOLEAN,
        within_date_range::BOOLEAN,
        within_time_window::BOOLEAN,
        shift_end_timestamp::TIMESTAMP,
        past_completion::BOOLEAN,
        status_result::TEXT;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION debug_shift_status(INTEGER, TIMESTAMP WITH TIME ZONE) IS 
'Debug function for shift status evaluation with manual-only completion policy';