#!/bin/bash

# Test script for backup functionality
# This script tests the backup functions without running the full deployment

# Source the main deployment script to get the backup functions
source ./deploy-hauling-qr-ubuntu.sh

# Set up test environment
export BACKUP_BASE_DIR="/tmp/test-hauling-backups"
export BACKUP_TIMESTAMP="test_$(date +%Y%m%d_%H%M%S)"
export BACKUP_DIR="$BACKUP_BASE_DIR/$BACKUP_TIMESTAMP"
export DOMAIN_NAME="test.example.com"
export ENV_MODE="test"
export VERSION="1.0.0-test"

# Create some test configuration files to backup
mkdir -p /tmp/test-configs/nginx/sites-available
mkdir -p /tmp/test-configs/ssl
echo "# Test nginx config" > /tmp/test-configs/nginx/nginx.conf
echo "# Test site config" > /tmp/test-configs/nginx/sites-available/test-site
echo "# Test SSL cert" > /tmp/test-configs/ssl/test.crt
echo "# Test environment" > /tmp/test-configs/.env

# Override some paths for testing
backup_config_file() {
    local source_file="$1"
    local backup_category="$2"
    local description="$3"
    
    # For testing, use test config files
    case "$source_file" in
        "/etc/nginx/nginx.conf")
            source_file="/tmp/test-configs/nginx/nginx.conf"
            ;;
        "/etc/nginx/sites-available/"*)
            source_file="/tmp/test-configs/nginx/sites-available/test-site"
            ;;
        "/etc/nginx/ssl/"*)
            source_file="/tmp/test-configs/ssl/test.crt"
            ;;
        ".env")
            source_file="/tmp/test-configs/.env"
            ;;
    esac
    
    if [[ ! -f "$source_file" ]]; then
        echo "Skipping backup of $source_file - file does not exist"
        return 0
    fi
    
    # Create category directory if it doesn't exist
    mkdir -p "$BACKUP_DIR/$backup_category"
    
    # Generate backup filename with original path preserved
    local backup_filename=$(echo "$1" | sed 's|/|_|g' | sed 's|^_||')
    local backup_path="$BACKUP_DIR/$backup_category/$backup_filename"
    
    # Copy the file with metadata preservation
    if cp -p "$source_file" "$backup_path"; then
        echo "Backed up: $1 -> $backup_path"
        
        # Add to backup metadata (simplified for testing)
        if [[ ! -f "$BACKUP_DIR/backup-metadata.json" ]]; then
            echo '{"backup_files":[]}' > "$BACKUP_DIR/backup-metadata.json"
        fi
        
        local temp_file=$(mktemp)
        jq ".backup_files += [{
            \"source\": \"$1\",
            \"backup_path\": \"$backup_path\",
            \"category\": \"$backup_category\",
            \"description\": \"$description\",
            \"timestamp\": \"$(date)\",
            \"size\": $(stat -c%s "$source_file" 2>/dev/null || echo 0),
            \"permissions\": \"$(stat -c%a "$source_file" 2>/dev/null || echo 'unknown')\",
            \"owner\": \"$(stat -c%U:%G "$source_file" 2>/dev/null || echo 'unknown')\"
        }]" "$BACKUP_DIR/backup-metadata.json" > "$temp_file" && mv "$temp_file" "$BACKUP_DIR/backup-metadata.json"
        
        return 0
    else
        echo "Failed to backup $1"
        return 1
    fi
}

echo "Testing backup functionality..."
echo "================================"

# Test 1: Initialize backup system
echo "Test 1: Initializing backup system"
init_backup_system
if [[ -d "$BACKUP_DIR" ]]; then
    echo "✓ Backup directory created: $BACKUP_DIR"
else
    echo "✗ Failed to create backup directory"
    exit 1
fi

# Test 2: Test individual backup functions
echo -e "\nTest 2: Testing backup functions"
backup_nginx_configs
backup_ssl_configs
backup_environment_configs

# Test 3: Verify backup integrity
echo -e "\nTest 3: Verifying backup integrity"
verify_backup_integrity
if [[ $? -eq 0 ]]; then
    echo "✓ Backup integrity verification passed"
else
    echo "✗ Backup integrity verification failed"
fi

# Test 4: List backups
echo -e "\nTest 4: Listing backups"
list_available_backups

# Test 5: Show backup details
echo -e "\nTest 5: Showing backup details"
show_backup_details "$BACKUP_TIMESTAMP"

# Test 6: Cleanup (remove test files)
echo -e "\nTest 6: Cleaning up test files"
rm -rf /tmp/test-configs
rm -rf "$BACKUP_BASE_DIR"
echo "✓ Test cleanup completed"

echo -e "\nAll backup function tests completed successfully!"