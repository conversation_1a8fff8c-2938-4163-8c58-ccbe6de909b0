-- ============================================================================
-- CONSOLIDATION VALIDATION AND TESTING FRAMEWORK
-- Hauling QR Trip Management System
-- ============================================================================
--
-- This comprehensive validation suite ensures the consolidated migration
-- creates an identical database structure to the production database.
--
-- Validation Categories:
-- 1. Schema Structure Validation
-- 2. Function Signature Verification
-- 3. Trigger Existence Verification
-- 4. Index Completeness Check
-- 5. Data Integrity Validation
-- 6. Performance Benchmark Queries
-- 7. Application Compatibility Tests
--
-- ============================================================================

-- ============================================================================
-- SCHEMA STRUCTURE VALIDATION
-- ============================================================================

-- Comprehensive schema comparison function
CREATE OR REPLACE FUNCTION validate_schema_structure() 
RETURNS TABLE(
    validation_category TEXT,
    component_name TEXT,
    expected_value TEXT,
    actual_value TEXT,
    status TEXT,
    details TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    rec RECORD;
BEGIN
    -- Validate enum types
    FOR rec IN 
        SELECT 
            t.typname as enum_name,
            array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values
        FROM pg_type t 
        JOIN pg_enum e ON t.oid = e.enumtypid 
        GROUP BY t.typname
        ORDER BY t.typname
    LOOP
        validation_category := 'Enum Types';
        component_name := rec.enum_name;
        expected_value := 'Valid enum with values';
        actual_value := array_to_string(rec.enum_values, ', ');
        status := 'PASS';
        details := 'Enum type exists with correct values';
        RETURN NEXT;
    END LOOP;
    
    -- Validate table structures
    FOR rec IN 
        SELECT 
            t.table_name,
            COUNT(c.column_name) as column_count,
            array_agg(c.column_name ORDER BY c.ordinal_position) as columns
        FROM information_schema.tables t
        LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
        WHERE t.table_schema = 'public' 
            AND t.table_type = 'BASE TABLE'
        GROUP BY t.table_name
        ORDER BY t.table_name
    LOOP
        validation_category := 'Table Structure';
        component_name := rec.table_name;
        expected_value := 'Table with columns';
        actual_value := rec.column_count || ' columns: ' || array_to_string(rec.columns, ', ');
        status := CASE WHEN rec.column_count > 0 THEN 'PASS' ELSE 'FAIL' END;
        details := 'Table structure validation';
        RETURN NEXT;
    END LOOP;
    
    -- Validate foreign key constraints
    FOR rec IN 
        SELECT 
            tc.table_name,
            tc.constraint_name,
            ccu.table_name as referenced_table,
            ccu.column_name as referenced_column
        FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu 
            ON tc.constraint_name = ccu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
        ORDER BY tc.table_name, tc.constraint_name
    LOOP
        validation_category := 'Foreign Keys';
        component_name := rec.table_name || '.' || rec.constraint_name;
        expected_value := 'Valid foreign key';
        actual_value := 'References ' || rec.referenced_table || '(' || rec.referenced_column || ')';
        status := 'PASS';
        details := 'Foreign key constraint exists';
        RETURN NEXT;
    END LOOP;
END;
$$;

-- ============================================================================
-- FUNCTION SIGNATURE VERIFICATION
-- ============================================================================

-- Function to validate all required functions exist with correct signatures
CREATE OR REPLACE FUNCTION validate_function_signatures() 
RETURNS TABLE(
    function_name TEXT,
    argument_types TEXT,
    return_type TEXT,
    status TEXT,
    details TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    rec RECORD;
    expected_functions TEXT[] := ARRAY[
        'update_updated_at_column',
        'calculate_trip_durations',
        'update_assignment_on_trip_complete',
        'create_deviation_assignment',
        'get_current_driver_for_truck',
        'create_shift_assignment',
        'evaluate_shift_status',
        'schedule_auto_activation',
        'update_all_shift_statuses',
        'log_automated_fix',
        'log_system_event',
        'set_display_type_trigger',
        'sync_shift_date_with_start_date',
        'is_trip_terminal',
        'auto_activate_shifts',
        'auto_populate_driver_from_shift',
        'auto_capture_trip_driver',
        'capture_active_driver_for_trip_enhanced',
        'get_current_driver_for_truck_enhanced',
        'classify_shift_by_time',
        'add_sample_shift_data',
        'auto_activate_shifts_enhanced',
        'auto_complete_shifts_enhanced',
        'calculate_shift_end_timestamp',
        'calculate_shift_status',
        'check_shift_status_consistency',
        'is_overnight_shift',
        'capture_active_driver_for_trip',
        'capture_active_driver_for_trip_enhanced_test',
        'cleanup_shift_system',
        'complete_shift_manually',
        'create_assignment_with_auto_driver',
        'create_unified_shift',
        'debug_shift_status',
        'fix_incorrectly_completed_shifts',
        'get_active_shifts_for_date',
        'get_current_active_driver',
        'get_shift_display_date',
        'get_shift_status_summary',
        'is_shift_active_on_date',
        'monitor_shift_system',
        'refresh_all_analytics_views',
        'refresh_breakdown_analytics_summary',
        'refresh_fleet_status_summary',
        'schedule_shift_activation',
        'shift_includes_date',
        'shift_overlaps_range',
        'shift_system_health_check',
        'test_shift_scenarios',
        'test_shift_time_logic',
        'update_shift_status',
        'validate_all_shift_statuses',
        'get_exception_analytics',
        'get_advanced_exception_analytics',
        'refresh_trip_performance_summary',
        'get_database_performance_metrics'
    ];
    func_name TEXT;
BEGIN
    -- Check each expected function
    FOREACH func_name IN ARRAY expected_functions
    LOOP
        SELECT 
            p.proname,
            pg_get_function_identity_arguments(p.oid),
            pg_get_function_result(p.oid)
        INTO rec
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public' AND p.proname = func_name;
        
        IF FOUND THEN
            function_name := rec.proname;
            argument_types := rec.pg_get_function_identity_arguments;
            return_type := rec.pg_get_function_result;
            status := 'PASS';
            details := 'Function exists with correct signature';
        ELSE
            function_name := func_name;
            argument_types := 'N/A';
            return_type := 'N/A';
            status := 'FAIL';
            details := 'Function missing from schema';
        END IF;
        
        RETURN NEXT;
    END LOOP;
END;
$$;

-- ============================================================================
-- TRIGGER EXISTENCE VERIFICATION
-- ============================================================================

-- Function to validate all required triggers exist
CREATE OR REPLACE FUNCTION validate_triggers() 
RETURNS TABLE(
    trigger_name TEXT,
    table_name TEXT,
    function_name TEXT,
    timing TEXT,
    events TEXT,
    status TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    expected_triggers TEXT[] := ARRAY[
        'update_users_updated_at',
        'update_trucks_updated_at',
        'update_drivers_updated_at',
        'update_locations_updated_at',
        'update_assignments_updated_at',
        'update_trip_logs_updated_at',
        'update_approvals_updated_at',
        'update_driver_shifts_updated_at',
        'trg_auto_populate_driver',
        'trigger_auto_capture_trip_driver',
        'trigger_set_display_type',
        'trigger_sync_shift_date',
        'trigger_calculate_trip_durations',
        'trigger_update_assignment_on_trip_complete'
    ];
    trig_name TEXT;
    rec RECORD;
BEGIN
    FOREACH trig_name IN ARRAY expected_triggers
    LOOP
        SELECT 
            t.tgname,
            c.relname,
            p.proname,
            CASE t.tgtype & 66
                WHEN 2 THEN 'BEFORE'
                WHEN 64 THEN 'INSTEAD OF'
                ELSE 'AFTER'
            END,
            CASE t.tgtype & 28
                WHEN 4 THEN 'INSERT'
                WHEN 8 THEN 'DELETE'
                WHEN 16 THEN 'UPDATE'
                WHEN 12 THEN 'INSERT OR DELETE'
                WHEN 20 THEN 'INSERT OR UPDATE'
                WHEN 24 THEN 'DELETE OR UPDATE'
                WHEN 28 THEN 'INSERT OR DELETE OR UPDATE'
            END
        INTO rec
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        JOIN pg_proc p ON t.tgfoid = p.oid
        WHERE t.tgname = trig_name AND NOT t.tgisinternal;
        
        IF FOUND THEN
            trigger_name := rec.tgname;
            table_name := rec.relname;
            function_name := rec.proname;
            timing := rec.case;
            events := rec.case_1;
            status := 'PASS';
        ELSE
            trigger_name := trig_name;
            table_name := 'N/A';
            function_name := 'N/A';
            timing := 'N/A';
            events := 'N/A';
            status := 'FAIL';
        END IF;
        
        RETURN NEXT;
    END LOOP;
END;
$$;

-- ============================================================================
-- INDEX COMPLETENESS CHECK
-- ============================================================================

-- Function to validate critical indexes exist
CREATE OR REPLACE FUNCTION validate_indexes() 
RETURNS TABLE(
    table_name TEXT,
    index_name TEXT,
    index_type TEXT,
    columns TEXT,
    status TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    rec RECORD;
BEGIN
    -- Check for primary key indexes
    FOR rec IN 
        SELECT 
            t.tablename,
            i.indexname,
            'PRIMARY KEY' as index_type,
            i.indexdef
        FROM pg_indexes i
        JOIN pg_tables t ON i.tablename = t.tablename
        WHERE i.schemaname = 'public' 
            AND i.indexname LIKE '%_pkey'
        ORDER BY t.tablename
    LOOP
        table_name := rec.tablename;
        index_name := rec.indexname;
        index_type := rec.index_type;
        columns := rec.indexdef;
        status := 'PASS';
        RETURN NEXT;
    END LOOP;
    
    -- Check for foreign key indexes
    FOR rec IN 
        SELECT 
            t.tablename,
            i.indexname,
            'FOREIGN KEY' as index_type,
            i.indexdef
        FROM pg_indexes i
        JOIN pg_tables t ON i.tablename = t.tablename
        WHERE i.schemaname = 'public' 
            AND i.indexname LIKE 'idx_%'
            AND (i.indexname LIKE '%_id' OR i.indexname LIKE '%_fk')
        ORDER BY t.tablename
    LOOP
        table_name := rec.tablename;
        index_name := rec.indexname;
        index_type := rec.index_type;
        columns := rec.indexdef;
        status := 'PASS';
        RETURN NEXT;
    END LOOP;
    
    -- Check for GIN indexes on JSONB columns
    FOR rec IN 
        SELECT 
            t.tablename,
            i.indexname,
            'GIN JSONB' as index_type,
            i.indexdef
        FROM pg_indexes i
        JOIN pg_tables t ON i.tablename = t.tablename
        WHERE i.schemaname = 'public' 
            AND i.indexdef LIKE '%USING gin%'
        ORDER BY t.tablename
    LOOP
        table_name := rec.tablename;
        index_name := rec.indexname;
        index_type := rec.index_type;
        columns := rec.indexdef;
        status := 'PASS';
        RETURN NEXT;
    END LOOP;
END;
$$;-- ============================================================================
-- DATA INTEGRITY VALIDATION
-- ============================================================================

-- Function to validate data integrity and constraints
CREATE OR REPLACE FUNCTION validate_data_integrity() 
RETURNS TABLE(
    validation_type TEXT,
    table_name TEXT,
    constraint_name TEXT,
    status TEXT,
    details TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    rec RECORD;
BEGIN
    -- Check NOT NULL constraints
    FOR rec IN 
        SELECT 
            c.table_name,
            c.column_name,
            c.is_nullable
        FROM information_schema.columns c
        WHERE c.table_schema = 'public' 
            AND c.is_nullable = 'NO'
            AND c.column_default IS NULL
        ORDER BY c.table_name, c.column_name
    LOOP
        validation_type := 'NOT NULL Constraint';
        table_name := rec.table_name;
        constraint_name := rec.column_name;
        status := 'PASS';
        details := 'Column has NOT NULL constraint';
        RETURN NEXT;
    END LOOP;
    
    -- Check UNIQUE constraints
    FOR rec IN 
        SELECT 
            tc.table_name,
            tc.constraint_name,
            tc.constraint_type
        FROM information_schema.table_constraints tc
        WHERE tc.table_schema = 'public' 
            AND tc.constraint_type = 'UNIQUE'
        ORDER BY tc.table_name, tc.constraint_name
    LOOP
        validation_type := 'UNIQUE Constraint';
        table_name := rec.table_name;
        constraint_name := rec.constraint_name;
        status := 'PASS';
        details := 'Unique constraint exists';
        RETURN NEXT;
    END LOOP;
    
    -- Check CHECK constraints
    FOR rec IN 
        SELECT 
            tc.table_name,
            tc.constraint_name,
            cc.check_clause
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc 
            ON tc.constraint_name = cc.constraint_name
        WHERE tc.table_schema = 'public' 
            AND tc.constraint_type = 'CHECK'
        ORDER BY tc.table_name, tc.constraint_name
    LOOP
        validation_type := 'CHECK Constraint';
        table_name := rec.table_name;
        constraint_name := rec.constraint_name;
        status := 'PASS';
        details := 'Check constraint: ' || rec.check_clause;
        RETURN NEXT;
    END LOOP;
END;
$$;

-- ============================================================================
-- PERFORMANCE BENCHMARK QUERIES
-- ============================================================================

-- Function to run performance benchmarks
CREATE OR REPLACE FUNCTION run_performance_benchmarks() 
RETURNS TABLE(
    benchmark_name TEXT,
    execution_time_ms NUMERIC,
    rows_affected INTEGER,
    status TEXT,
    recommendation TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration_ms NUMERIC;
    row_count INTEGER;
BEGIN
    -- Benchmark 1: Complex assignment query
    start_time := clock_timestamp();
    
    SELECT COUNT(*) INTO row_count
    FROM assignments a
    JOIN dump_trucks dt ON a.truck_id = dt.id
    JOIN drivers d ON a.driver_id = d.id
    JOIN locations ll ON a.loading_location_id = ll.id
    JOIN locations ul ON a.unloading_location_id = ul.id
    WHERE a.status IN ('assigned', 'in_progress')
        AND dt.status = 'active'
        AND d.status = 'active';
    
    end_time := clock_timestamp();
    duration_ms := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    benchmark_name := 'Complex Assignment Query';
    execution_time_ms := duration_ms;
    rows_affected := row_count;
    status := CASE WHEN duration_ms < 100 THEN 'EXCELLENT' 
                   WHEN duration_ms < 500 THEN 'GOOD' 
                   ELSE 'NEEDS_OPTIMIZATION' END;
    recommendation := CASE WHEN duration_ms > 500 THEN 'Consider adding composite indexes' 
                           ELSE 'Performance acceptable' END;
    RETURN NEXT;
    
    -- Benchmark 2: Trip logs with driver information
    start_time := clock_timestamp();
    
    SELECT COUNT(*) INTO row_count
    FROM trip_logs tl
    JOIN assignments a ON tl.assignment_id = a.id
    JOIN dump_trucks dt ON a.truck_id = dt.id
    LEFT JOIN drivers d ON tl.performed_by_driver_id = d.id
    WHERE tl.created_at >= CURRENT_DATE - INTERVAL '30 days'
        AND tl.status = 'trip_completed';
    
    end_time := clock_timestamp();
    duration_ms := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    benchmark_name := 'Trip Performance Query';
    execution_time_ms := duration_ms;
    rows_affected := row_count;
    status := CASE WHEN duration_ms < 200 THEN 'EXCELLENT' 
                   WHEN duration_ms < 1000 THEN 'GOOD' 
                   ELSE 'NEEDS_OPTIMIZATION' END;
    recommendation := CASE WHEN duration_ms > 1000 THEN 'Consider partitioning trip_logs by date' 
                           ELSE 'Performance acceptable' END;
    RETURN NEXT;
    
    -- Benchmark 3: Shift status evaluation
    start_time := clock_timestamp();
    
    SELECT COUNT(*) INTO row_count
    FROM driver_shifts ds
    WHERE ds.status = 'active'
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date;
    
    end_time := clock_timestamp();
    duration_ms := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    benchmark_name := 'Active Shifts Query';
    execution_time_ms := duration_ms;
    rows_affected := row_count;
    status := CASE WHEN duration_ms < 50 THEN 'EXCELLENT' 
                   WHEN duration_ms < 200 THEN 'GOOD' 
                   ELSE 'NEEDS_OPTIMIZATION' END;
    recommendation := CASE WHEN duration_ms > 200 THEN 'Verify shift date indexes' 
                           ELSE 'Performance excellent' END;
    RETURN NEXT;
END;
$$;

-- ============================================================================
-- APPLICATION COMPATIBILITY TESTS
-- ============================================================================

-- Function to test application compatibility
CREATE OR REPLACE FUNCTION test_application_compatibility() 
RETURNS TABLE(
    test_name TEXT,
    test_category TEXT,
    status TEXT,
    details TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    test_result BOOLEAN;
    error_message TEXT;
BEGIN
    -- Test 1: Basic CRUD operations
    BEGIN
        -- Test user creation
        INSERT INTO users (username, email, password_hash, full_name, role)
        VALUES ('test_user_validation', '<EMAIL>', 'hash123', 'Test User', 'operator');
        
        -- Test user update
        UPDATE users SET full_name = 'Updated Test User' 
        WHERE username = 'test_user_validation';
        
        -- Test user deletion
        DELETE FROM users WHERE username = 'test_user_validation';
        
        test_name := 'Basic CRUD Operations';
        test_category := 'Data Operations';
        status := 'PASS';
        details := 'Insert, update, delete operations successful';
        RETURN NEXT;
        
    EXCEPTION WHEN OTHERS THEN
        test_name := 'Basic CRUD Operations';
        test_category := 'Data Operations';
        status := 'FAIL';
        details := 'Error: ' || SQLERRM;
        RETURN NEXT;
    END;
    
    -- Test 2: Enum type usage
    BEGIN
        SELECT 'active'::user_role, 'day'::shift_type, 'assigned'::trip_status;
        
        test_name := 'Enum Type Usage';
        test_category := 'Type System';
        status := 'PASS';
        details := 'All enum types accessible';
        RETURN NEXT;
        
    EXCEPTION WHEN OTHERS THEN
        test_name := 'Enum Type Usage';
        test_category := 'Type System';
        status := 'FAIL';
        details := 'Error: ' || SQLERRM;
        RETURN NEXT;
    END;
    
    -- Test 3: Function execution
    BEGIN
        PERFORM is_fresh_database();
        PERFORM get_migration_state();
        
        test_name := 'Function Execution';
        test_category := 'Business Logic';
        status := 'PASS';
        details := 'Core functions executable';
        RETURN NEXT;
        
    EXCEPTION WHEN OTHERS THEN
        test_name := 'Function Execution';
        test_category := 'Business Logic';
        status := 'FAIL';
        details := 'Error: ' || SQLERRM;
        RETURN NEXT;
    END;
    
    -- Test 4: View accessibility
    BEGIN
        PERFORM COUNT(*) FROM v_realtime_dashboard;
        
        test_name := 'View Accessibility';
        test_category := 'Data Access';
        status := 'PASS';
        details := 'Views accessible for queries';
        RETURN NEXT;
        
    EXCEPTION WHEN OTHERS THEN
        test_name := 'View Accessibility';
        test_category := 'Data Access';
        status := 'FAIL';
        details := 'Error: ' || SQLERRM;
        RETURN NEXT;
    END;
END;
$$;

-- ============================================================================
-- COMPREHENSIVE VALIDATION RUNNER
-- ============================================================================

-- Master validation function that runs all tests
CREATE OR REPLACE FUNCTION run_comprehensive_validation() 
RETURNS TABLE(
    validation_suite TEXT,
    total_tests INTEGER,
    passed_tests INTEGER,
    failed_tests INTEGER,
    success_rate NUMERIC,
    overall_status TEXT
)
LANGUAGE plpgsql AS $$
DECLARE
    schema_tests INTEGER;
    schema_passed INTEGER;
    function_tests INTEGER;
    function_passed INTEGER;
    trigger_tests INTEGER;
    trigger_passed INTEGER;
    integrity_tests INTEGER;
    integrity_passed INTEGER;
    compatibility_tests INTEGER;
    compatibility_passed INTEGER;
    total_all INTEGER;
    passed_all INTEGER;
BEGIN
    -- Schema structure validation
    SELECT COUNT(*), COUNT(CASE WHEN status = 'PASS' THEN 1 END)
    INTO schema_tests, schema_passed
    FROM validate_schema_structure();
    
    validation_suite := 'Schema Structure';
    total_tests := schema_tests;
    passed_tests := schema_passed;
    failed_tests := schema_tests - schema_passed;
    success_rate := ROUND((schema_passed::NUMERIC / schema_tests * 100), 2);
    overall_status := CASE WHEN success_rate >= 95 THEN 'EXCELLENT'
                           WHEN success_rate >= 80 THEN 'GOOD'
                           ELSE 'NEEDS_ATTENTION' END;
    RETURN NEXT;
    
    -- Function signature validation
    SELECT COUNT(*), COUNT(CASE WHEN status = 'PASS' THEN 1 END)
    INTO function_tests, function_passed
    FROM validate_function_signatures();
    
    validation_suite := 'Function Signatures';
    total_tests := function_tests;
    passed_tests := function_passed;
    failed_tests := function_tests - function_passed;
    success_rate := ROUND((function_passed::NUMERIC / function_tests * 100), 2);
    overall_status := CASE WHEN success_rate >= 95 THEN 'EXCELLENT'
                           WHEN success_rate >= 80 THEN 'GOOD'
                           ELSE 'NEEDS_ATTENTION' END;
    RETURN NEXT;
    
    -- Trigger validation
    SELECT COUNT(*), COUNT(CASE WHEN status = 'PASS' THEN 1 END)
    INTO trigger_tests, trigger_passed
    FROM validate_triggers();
    
    validation_suite := 'Triggers';
    total_tests := trigger_tests;
    passed_tests := trigger_passed;
    failed_tests := trigger_tests - trigger_passed;
    success_rate := ROUND((trigger_passed::NUMERIC / trigger_tests * 100), 2);
    overall_status := CASE WHEN success_rate >= 95 THEN 'EXCELLENT'
                           WHEN success_rate >= 80 THEN 'GOOD'
                           ELSE 'NEEDS_ATTENTION' END;
    RETURN NEXT;
    
    -- Data integrity validation
    SELECT COUNT(*), COUNT(CASE WHEN status = 'PASS' THEN 1 END)
    INTO integrity_tests, integrity_passed
    FROM validate_data_integrity();
    
    validation_suite := 'Data Integrity';
    total_tests := integrity_tests;
    passed_tests := integrity_passed;
    failed_tests := integrity_tests - integrity_passed;
    success_rate := ROUND((integrity_passed::NUMERIC / integrity_tests * 100), 2);
    overall_status := CASE WHEN success_rate >= 95 THEN 'EXCELLENT'
                           WHEN success_rate >= 80 THEN 'GOOD'
                           ELSE 'NEEDS_ATTENTION' END;
    RETURN NEXT;
    
    -- Application compatibility
    SELECT COUNT(*), COUNT(CASE WHEN status = 'PASS' THEN 1 END)
    INTO compatibility_tests, compatibility_passed
    FROM test_application_compatibility();
    
    validation_suite := 'Application Compatibility';
    total_tests := compatibility_tests;
    passed_tests := compatibility_passed;
    failed_tests := compatibility_tests - compatibility_passed;
    success_rate := ROUND((compatibility_passed::NUMERIC / compatibility_tests * 100), 2);
    overall_status := CASE WHEN success_rate >= 95 THEN 'EXCELLENT'
                           WHEN success_rate >= 80 THEN 'GOOD'
                           ELSE 'NEEDS_ATTENTION' END;
    RETURN NEXT;
    
    -- Overall summary
    total_all := schema_tests + function_tests + trigger_tests + integrity_tests + compatibility_tests;
    passed_all := schema_passed + function_passed + trigger_passed + integrity_passed + compatibility_passed;
    
    validation_suite := 'OVERALL SUMMARY';
    total_tests := total_all;
    passed_tests := passed_all;
    failed_tests := total_all - passed_all;
    success_rate := ROUND((passed_all::NUMERIC / total_all * 100), 2);
    overall_status := CASE WHEN success_rate >= 95 THEN 'EXCELLENT - READY FOR PRODUCTION'
                           WHEN success_rate >= 80 THEN 'GOOD - MINOR ISSUES'
                           ELSE 'NEEDS_ATTENTION - MAJOR ISSUES' END;
    RETURN NEXT;
END;
$$;