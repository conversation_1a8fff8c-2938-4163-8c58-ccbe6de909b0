-- Migration: Add stopped columns for trip tracking
-- Description: Creates stopped columns for trip tracking and ensures proper configuration
-- Version: 028
-- Date: 2025-07-09

-- Add stopped-related columns to trip_logs for better tracking
DO $$
BEGIN
    -- Add stopped_reported_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_reported_at'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_reported_at TIMESTAMP;
        RAISE NOTICE 'Added stopped_reported_at column';
    END IF;

    -- Add stopped_reason column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_reason'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_reason TEXT;
        RAISE NOTICE 'Added stopped_reason column';
    END IF;

    -- Add stopped_resolved_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_resolved_at'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_resolved_at TIMESTAMP;
        RAISE NOTICE 'Added stopped_resolved_at column';
    END IF;

    -- Add stopped_resolved_by column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'stopped_resolved_by'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN stopped_resolved_by INTEGER REFERENCES users(id);
        RAISE NOTICE 'Added stopped_resolved_by column';
    END IF;

    -- Add previous_status column to track status before stopped
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' AND column_name = 'previous_status'
    ) THEN
        ALTER TABLE trip_logs ADD COLUMN previous_status trip_status;
        RAISE NOTICE 'Added previous_status column';
    END IF;
END $$;

-- Create indexes for stopped status queries (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trip_logs_stopped'
    ) THEN
        CREATE INDEX idx_trip_logs_stopped ON trip_logs(status) WHERE status = 'stopped';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trip_logs_stopped_date'
    ) THEN
        CREATE INDEX idx_trip_logs_stopped_date ON trip_logs(stopped_reported_at) WHERE stopped_reported_at IS NOT NULL;
    END IF;
END $$;

-- Create additional indexes for stopped columns
CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_reported_at ON trip_logs(stopped_reported_at);
CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_resolved_at ON trip_logs(stopped_resolved_at);
CREATE INDEX IF NOT EXISTS idx_trip_logs_stopped_status ON trip_logs(status) WHERE status = 'stopped';

-- Add comments for documentation
COMMENT ON COLUMN trip_logs.stopped_reported_at IS 'Timestamp when the trip was stopped/reported as having issues';
COMMENT ON COLUMN trip_logs.stopped_reason IS 'Reason why the trip was stopped (mechanical issue, accident, etc.)';
COMMENT ON COLUMN trip_logs.stopped_resolved_at IS 'Timestamp when the stopped trip was resolved';
COMMENT ON COLUMN trip_logs.stopped_resolved_by IS 'User ID who resolved the stopped trip';

-- Verify the migration
DO $$
BEGIN
    -- Check if all stopped columns exist
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
        AND column_name IN ('stopped_reported_at', 'stopped_reason', 'stopped_resolved_at', 'stopped_resolved_by')
        GROUP BY table_name
        HAVING COUNT(*) = 4
    ) THEN
        RAISE NOTICE 'Migration verification: All stopped columns successfully created';
    ELSE
        RAISE WARNING 'Migration verification: Some stopped columns may be missing';
    END IF;
END $$;
