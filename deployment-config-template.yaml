# ============================================================================
# HAULING QR TRIP SYSTEM - DEPLOYMENT CONFIGURATION TEMPLATE (YAML)
# ============================================================================
# Copy this file and customize for your deployment
# Usage: ./deploy-hauling-qr-ubuntu.sh --config-file your-config.yaml

# Domain Configuration
domain:
  name: truckhaul.top
  sslMode: cloudflare
  cloudflare:
    mode: full
    apiToken: ""
    zoneId: ""
    optimization:
      minifyHtml: true
      minifyCss: true
      minifyJs: true
      brotli: true
      cacheLevel: standard
      browserCacheTtl: 14400

# Database Configuration
database:
  password: ""  # Leave empty for auto-generation
  host: localhost
  port: 5432
  name: hauling_qr_system
  user: hauling_app
  poolMax: 25
  poolMin: 5
  optimization:
    sharedBuffers: 256MB
    effectiveCacheSize: 1GB
    workMem: 4MB
    maxConnections: 100

# Security Configuration
security:
  jwtSecret: ""  # Leave empty for auto-generation
  jwtExpiration: 24h
  jwtRefreshSecret: ""  # Leave empty for auto-generation
  jwtRefreshExpiration: 7d
  fail2ban:
    banTime: 3600
    maxRetry: 5
  firewall:
    enable: true
  rateLimiting:
    api: 20r/s
    auth: 5r/m
    general: 10r/s

# Admin User Configuration
admin:
  username: admin
  password: ""  # Leave empty for auto-generation
  email: <EMAIL>

# Repository Configuration
repository:
  url: https://github.com/your-org/hauling-qr-trip-system.git
  branch: main

# Features Configuration
features:
  monitoring:
    enabled: true
    healthCheckInterval: "*/5 * * * *"
    performanceCheckInterval: "*/10 * * * *"
    reportGenerationTime: "0 6 * * *"
  backups:
    enabled: true
    schedule: "0 2 * * *"
    retentionDays: 7
    fullBackupSchedule: "0 3 * * 0"
    compression: true

# Environment Configuration
environment:
  mode: production  # Options: production, staging, development
  nodeVersion: "18"
  postgresVersion: "15"
  appUser: hauling_app
  appDir: /var/www/hauling-qr-system

# Performance Configuration
performance:
  pm2Instances: max  # Options: max, number (e.g., 4)
  maxMemoryRestart: 2G
  nodeMaxOldSpace: 2048

# Logging Configuration
logging:
  level: info  # Options: error, warn, info, debug
  rotationSize: 100M
  retentionDays: 52

# Email Configuration (for alerts)
email:
  smtpHost: smtp.gmail.com
  smtpPort: 587
  smtpUser: <EMAIL>
  smtpPassword: ""
  alertEmail: <EMAIL>