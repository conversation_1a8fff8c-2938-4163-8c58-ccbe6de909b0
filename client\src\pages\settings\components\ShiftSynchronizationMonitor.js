import React, { useState, useEffect } from 'react';
import { getApiBaseUrl } from '../../../utils/network-utils';

const ShiftSynchronizationMonitor = () => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch synchronization status
  const fetchStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/shifts/sync-status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch status: ${response.statusText}`);
      }

      const result = await response.json();
      setStatus(result.data);

    } catch (err) {
      console.error('Error fetching sync status:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Perform manual sync check
  const performSyncCheck = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/shifts/sync-check`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to perform sync check: ${response.statusText}`);
      }

      const result = await response.json();
      setStatus(result.data);

    } catch (err) {
      console.error('Error performing sync check:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Start/Stop monitoring
  const toggleMonitoring = async (start) => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const endpoint = start ? 'sync-start' : 'sync-stop';
      
      const response = await fetch(`${apiUrl}/shifts/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to ${start ? 'start' : 'stop'} monitoring: ${response.statusText}`);
      }

      const result = await response.json();
      setStatus(result.data);

    } catch (err) {
      console.error(`Error ${start ? 'starting' : 'stopping'} monitoring:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Clear issues
  const clearIssues = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/shifts/sync-clear-issues`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to clear issues: ${response.statusText}`);
      }

      const result = await response.json();
      setStatus(result.data);

    } catch (err) {
      console.error('Error clearing issues:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Apply overnight shift fix
  const applyOvernightShiftFix = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/shifts/schedule-auto-activation`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to apply overnight fix: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Show success message
      alert(`✅ Overnight shift fix applied successfully!\n\nActivated: ${result.data.activated_count} shifts\nCompleted: ${result.data.completed_count} shifts`);
      
      // Refresh status
      await fetchStatus();

    } catch (err) {
      console.error('Error applying overnight fix:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    fetchStatus();

    if (autoRefresh) {
      const interval = setInterval(fetchStatus, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get status indicator
  const getStatusIndicator = () => {
    if (!status) return { color: 'text-gray-500', icon: '⚪', text: 'Unknown' };
    
    if (!status.isRunning) {
      return { color: 'text-red-500', icon: '🔴', text: 'Stopped' };
    }
    
    if (status.issueCount === 0) {
      return { color: 'text-green-500', icon: '🟢', text: 'All Systems Synchronized' };
    }
    
    const hasHighSeverity = status.issues?.some(issue => issue.severity === 'high');
    if (hasHighSeverity) {
      return { color: 'text-red-500', icon: '🔴', text: 'Critical Issues Detected' };
    }
    
    return { color: 'text-yellow-500', icon: '🟡', text: 'Issues Detected' };
  };

  const statusIndicator = getStatusIndicator();

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200">
      <div className="px-6 py-4 border-b border-secondary-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-secondary-900">
              🔄 Shift Synchronization Monitor
            </h3>
            <p className="text-sm text-secondary-500 mt-1">
              Real-time monitoring of synchronization between Shift Management and Assignment Management
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusIndicator.color}`}>
              {statusIndicator.icon} {statusIndicator.text}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">❌ {error}</p>
          </div>
        )}

        {/* Status Overview */}
        {status && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-secondary-50 rounded-lg p-4">
              <div className="text-sm font-medium text-secondary-900">Monitor Status</div>
              <div className={`text-lg font-semibold ${status.isRunning ? 'text-green-600' : 'text-red-600'}`}>
                {status.isRunning ? 'Running' : 'Stopped'}
              </div>
            </div>
            
            <div className="bg-secondary-50 rounded-lg p-4">
              <div className="text-sm font-medium text-secondary-900">Issues Detected</div>
              <div className={`text-lg font-semibold ${status.issueCount > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
                {status.issueCount}
              </div>
            </div>
            
            <div className="bg-secondary-50 rounded-lg p-4">
              <div className="text-sm font-medium text-secondary-900">Last Check</div>
              <div className="text-lg font-semibold text-secondary-900">
                {status.lastCheck ? new Date(status.lastCheck).toLocaleTimeString() : 'Never'}
              </div>
            </div>
          </div>
        )}

        {/* Control Buttons */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={performSyncCheck}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? '⏳' : '🔍'} Manual Check
          </button>

          {status?.isRunning ? (
            <button
              onClick={() => toggleMonitoring(false)}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              {loading ? '⏳' : '⏹️'} Stop Monitor
            </button>
          ) : (
            <button
              onClick={() => toggleMonitoring(true)}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              {loading ? '⏳' : '▶️'} Start Monitor
            </button>
          )}

          {status?.issueCount > 0 && (
            <button
              onClick={clearIssues}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? '⏳' : '🧹'} Clear Issues
            </button>
          )}
          
          <button
            onClick={applyOvernightShiftFix}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
            title="Fix Assignment Management display issues"
          >
            {loading ? '⏳' : '🛠️'} Fix Assignment Display Issues
          </button>

          <label className="inline-flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-secondary-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm text-secondary-700">Auto-refresh</span>
          </label>
        </div>

        {/* Issues List */}
        {status?.issues && status.issues.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-secondary-900 mb-3">
              🚨 Detected Issues ({status.issues.length})
            </h4>
            <div className="space-y-3">
              {status.issues.map((issue, index) => (
                <div key={index} className="border border-secondary-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(issue.severity)}`}>
                          {issue.severity?.toUpperCase()}
                        </span>
                        <span className="text-sm font-medium text-secondary-900">
                          {issue.type?.replace(/_/g, ' ').toUpperCase()}
                        </span>
                      </div>
                      <p className="text-sm text-secondary-700 mb-2">{issue.message}</p>
                      
                      {issue.shifts && (
                        <div className="text-xs text-secondary-500">
                          Affected shifts: {issue.shifts.map(s => `${s.truck_number}-${s.full_name}`).join(', ')}
                        </div>
                      )}
                      
                      {issue.trucks && (
                        <div className="text-xs text-secondary-500">
                          Affected trucks: {issue.trucks.map(t => t.truck_number).join(', ')}
                        </div>
                      )}
                    </div>
                    
                    {issue.count && (
                      <div className="text-right">
                        <div className="text-lg font-semibold text-secondary-900">{issue.count}</div>
                        <div className="text-xs text-secondary-500">items</div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Assignment Management Fix Section */}
        <div className="mt-6 p-4 bg-purple-50 border border-purple-200 rounded-md">
          <h5 className="text-sm font-medium text-purple-900 mb-2">🎯 Assignment Management Fix</h5>
          <p className="text-sm text-purple-700 mb-3">
            If Assignment Management shows "⚠️ No Active Shift" instead of proper shift info, click the purple button above to fix it instantly.
          </p>
          <ul className="text-sm text-purple-700 space-y-1">
            <li>• Fixes overnight shift display issues (8 PM - 6 AM)</li>
            <li>• Resolves "No Active Shift" display problems</li>
            <li>• Updates Assignment Management to show correct shift info</li>
            <li>• No terminal commands needed - just click the button</li>
          </ul>
        </div>

        {/* Help Text */}
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h5 className="text-sm font-medium text-blue-900 mb-2">ℹ️ How it works:</h5>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Automatically detects and fixes scheduled shifts that should be active</li>
            <li>• Monitors synchronization between Shift Management and Assignment Management</li>
            <li>• Identifies and resolves orphaned active shifts outside their time range</li>
            <li>• Runs continuous monitoring every 30 seconds when enabled</li>
            <li>• Auto-fixes most issues automatically to maintain system consistency</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ShiftSynchronizationMonitor;
