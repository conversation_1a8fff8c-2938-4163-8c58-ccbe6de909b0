/**
 * Manual Shift Management Routes
 * Purpose: API endpoints for manual shift status control
 */

const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const manualShiftManagementService = require('../services/ManualShiftManagementService');

// Validation middleware
const validateShiftId = (req, res, next) => {
  const shiftId = parseInt(req.params.id);
  if (isNaN(shiftId) || shiftId <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Invalid shift ID provided'
    });
  }
  req.validatedShiftId = shiftId;
  next();
};

const validateRequestBody = (schema) => (req, res, next) => {
  const errors = [];
  
  for (const [field, validator] of Object.entries(schema)) {
    const value = req.body[field];
    try {
      validator(value, field);
    } catch (error) {
      errors.push(error.message);
    }
  }
  
  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: errors.join(', ')
    });
  }
  
  next();
};

const stringValidator = (value, fieldName) => {
  if (value && typeof value !== 'string') {
    throw new Error(`${fieldName} must be a string`);
  }
};

// Error response helper
const handleError = (res, error, operation) => {
  console.error(`Error ${operation}:`, error);
  
  // Handle validation errors with 400 status
  if (error.message.includes('Invalid') || error.message.includes('must be')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  // Handle service errors
  res.status(500).json({
    success: false,
    message: `Server error while ${operation}`,
    error: error.message
  });
};

/**
 * @route   GET /api/manual-shift-management/active
 * @desc    Get all active shifts
 * @access  Private (Admin)
 */
router.get('/active', auth, async (req, res) => {
  try {
    const activeShifts = await manualShiftManagementService.getActiveShifts();
    
    res.json({
      success: true,
      data: activeShifts
    });
  } catch (error) {
    handleError(res, error, 'getting active shifts');
  }
});

/**
 * @route   GET /api/manual-shift-management/scheduled
 * @desc    Get all scheduled shifts
 * @access  Private (Admin)
 */
router.get('/scheduled', auth, async (req, res) => {
  try {
    const scheduledShifts = await manualShiftManagementService.getScheduledShifts();
    
    res.json({
      success: true,
      data: scheduledShifts
    });
  } catch (error) {
    handleError(res, error, 'getting scheduled shifts');
  }
});

/**
 * @route   POST /api/manual-shift-management/complete/:id
 * @desc    Manually complete a shift
 * @access  Private (Admin)
 */
router.post('/complete/:id', 
  auth,
  validateShiftId,
  validateRequestBody({ notes: stringValidator }),
  async (req, res) => {
    try {
      const { notes } = req.body;
      const userId = req.user?.id || 1; // Use authenticated user ID or fallback
      
      const result = await manualShiftManagementService.completeShift(
        req.validatedShiftId,
        userId,
        notes || ''
      );
      
      res.json({
        success: true,
        message: 'Shift completed successfully',
        data: result.shift
      });
    } catch (error) {
      handleError(res, error, 'completing shift');
    }
  }
);

/**
 * @route   POST /api/manual-shift-management/cancel/:id
 * @desc    Manually cancel a shift
 * @access  Private (Admin)
 */
router.post('/cancel/:id', 
  auth,
  validateShiftId,
  validateRequestBody({ reason: stringValidator }),
  async (req, res) => {
    try {
      const { reason } = req.body;
      const userId = req.user?.id || 1; // Use authenticated user ID or fallback
      
      const result = await manualShiftManagementService.cancelShift(
        req.validatedShiftId,
        userId,
        reason || ''
      );
      
      res.json({
        success: true,
        message: 'Shift cancelled successfully',
        data: result
      });
    } catch (error) {
      handleError(res, error, 'cancelling shift');
    }
  }
);

/**
 * @route   POST /api/manual-shift-management/refresh
 * @desc    Force refresh shift statuses
 * @access  Private (Admin)
 */
router.post('/refresh', auth, async (req, res) => {
  try {
    const userId = req.user?.id || 1; // Use authenticated user ID or fallback
    
    const result = await manualShiftManagementService.forceRefreshStatuses(userId);
    
    res.json({
      success: true,
      message: 'Shift statuses refreshed successfully',
      data: result
    });
  } catch (error) {
    handleError(res, error, 'refreshing shift statuses');
  }
});

/**
 * @route   GET /api/manual-shift-management/summary
 * @desc    Get shift status summary
 * @access  Private (Admin)
 */
router.get('/summary', auth, async (req, res) => {
  try {
    const summary = await manualShiftManagementService.getShiftStatusSummary();
    
    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    handleError(res, error, 'getting shift status summary');
  }
});

module.exports = router;