#!/usr/bin/env node

/**
 * Simple Database Reset Script
 * 
 * Completely resets the database with fresh tables using the consolidated schema.
 * This is the ONLY file you need to run for a fresh database.
 * 
 * Usage:
 *   node reset.js           # Reset with confirmation
 *   node reset.js --force   # Reset without confirmation
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const readline = require('readline');
require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
};

const INIT_SQL_PATH = path.join(__dirname, 'init.sql');

async function confirmReset(force = false) {
  if (force) {
    return true;
  }

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    console.log('\n⚠️  WARNING: This will completely reset your database!');
    console.log('   - All existing data will be permanently deleted');
    console.log('   - All tables will be dropped and recreated');
    console.log('   - Fresh schema will be created from init.sql\n');
    
    rl.question('Are you sure you want to continue? (yes/no): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

async function resetDatabase(force = false) {
  console.log('🔄 Starting database reset...\n');
  
  // Confirm reset unless forced
  const confirmed = await confirmReset(force);
  if (!confirmed) {
    console.log('❌ Reset cancelled by user');
    process.exit(0);
  }

  const pool = new Pool(dbConfig);
  const client = await pool.connect();

  try {
    console.log('🗑️  Dropping existing schema...');
    
    // Drop everything in correct order
    await client.query(`
      -- Drop materialized views
      DROP MATERIALIZED VIEW IF EXISTS mv_trip_performance_summary CASCADE;
      
      -- Drop regular views
      DROP VIEW IF EXISTS v_realtime_dashboard CASCADE;
      DROP VIEW IF EXISTS v_active_exceptions CASCADE;
      DROP VIEW IF EXISTS v_trip_performance CASCADE;
      DROP VIEW IF EXISTS v_trip_summary CASCADE;
      DROP VIEW IF EXISTS v_active_assignments CASCADE;
      DROP VIEW IF EXISTS v_workflow_analytics CASCADE;
      DROP VIEW IF EXISTS v_dynamic_assignment_analytics CASCADE;
      
      -- Drop tables in dependency order
      DROP TABLE IF EXISTS scan_logs CASCADE;
      DROP TABLE IF EXISTS approvals CASCADE;
      DROP TABLE IF EXISTS trip_logs CASCADE;
      DROP TABLE IF EXISTS assignments CASCADE;
      DROP TABLE IF EXISTS locations CASCADE;
      DROP TABLE IF EXISTS drivers CASCADE;
      DROP TABLE IF EXISTS dump_trucks CASCADE;
      DROP TABLE IF EXISTS users CASCADE;
      DROP TABLE IF EXISTS driver_shifts CASCADE;
      DROP TABLE IF EXISTS shift_handovers CASCADE;
      DROP TABLE IF EXISTS system_tasks CASCADE;
      DROP TABLE IF EXISTS system_health_logs CASCADE;
      DROP TABLE IF EXISTS automated_fix_logs CASCADE;
      DROP TABLE IF EXISTS migrations CASCADE;
      
      -- Drop types
      DROP TYPE IF EXISTS user_role CASCADE;
      DROP TYPE IF EXISTS truck_status CASCADE;
      DROP TYPE IF EXISTS driver_status CASCADE;
      DROP TYPE IF EXISTS location_type CASCADE;
      DROP TYPE IF EXISTS assignment_status CASCADE;
      DROP TYPE IF EXISTS trip_status CASCADE;
      DROP TYPE IF EXISTS approval_status CASCADE;
      DROP TYPE IF EXISTS scan_type CASCADE;
    `);
    
    console.log('✅ Schema dropped successfully');

    console.log('📋 Creating fresh schema...');
    
    if (!fs.existsSync(INIT_SQL_PATH)) {
      throw new Error(`init.sql not found at: ${INIT_SQL_PATH}`);
    }
    
    const initSQL = fs.readFileSync(INIT_SQL_PATH, 'utf8');
    
    await client.query('BEGIN');
    await client.query(initSQL);
    await client.query('COMMIT');
    
    console.log('✅ Fresh schema created successfully');

    console.log('\n🎉 Database reset completed!');
    console.log('✅ All tables recreated with consolidated schema');
    console.log('✅ All migrations already included in init.sql');
    console.log('✅ Ready for use');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('\n❌ Database reset failed:', error.message);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
const force = args.includes('--force') || args.includes('-f');
const help = args.includes('--help') || args.includes('-h');

if (help) {
  console.log(`
🔄 Simple Database Reset Script

Usage: node reset.js [options]

Options:
  --force, -f     Skip confirmation prompt
  --help, -h      Show this help message

Examples:
  node reset.js           # Reset with confirmation
  node reset.js --force   # Reset without confirmation

This is the ONLY file you need to run for a fresh database.
`);
  process.exit(0);
}

// Run the reset
resetDatabase(force);