-- Migration: Fix debug_shift_status function column ambiguity
-- Purpose: Resolve column reference ambiguity in RETURN QUERY SELECT
-- Created: 2025-07-18

-- Drop and recreate the debug function with fixed column references
DROP FUNCTION IF EXISTS debug_shift_status(INTEGER, TIMESTAMP WITH TIME ZONE) CASCADE;

-- Create the CORRECTED debug_shift_status function with explicit column references
CREATE OR REPLACE FUNCTION debug_shift_status(
    p_shift_id INTEGER,
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE (
    shift_id INTEGER,
    shift_start_date DATE,
    shift_end_date DATE,
    shift_start_time TIME,
    shift_end_time TIME,
    current_date_val DATE,
    current_time_val TIME,
    is_overnight BOOLEAN,
    is_within_date_range BOOLEAN,
    is_within_time_window BOOLEAN,
    shift_end_datetime TIMESTAMP,
    is_past_completion BOOLEAN,
    calculated_status TEXT
) AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
    v_new_status TEXT;
    v_shift_end_datetime TIMESTAMP;
BEGIN
    -- Get shift details
    SELECT
        id,
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status,
        recurrence_pattern
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- Extract current date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if shift spans overnight (night shift logic)
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- Enhanced date range validation with unified approach
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Enhanced time window validation with proper overnight logic
    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic for overnight spans
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
    ELSE
        -- Day shift: Use simple BETWEEN logic for same-day shifts
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
    END IF;

    -- Enhanced completion logic with proper overnight handling (FOR DEBUG ONLY)
    IF v_is_overnight THEN
        -- For overnight shifts: would be completed when past end_time on the next day
        v_shift_end_datetime := (v_shift.end_date + INTERVAL '1 day')::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    ELSE
        -- For day shifts: would be completed when past end_time on the same day
        v_shift_end_datetime := v_shift.end_date::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    END IF;

    -- CORRECTED: Apply status rules with MANUAL-ONLY completion
    -- DEBUG NOTE: This shows what the status WOULD be, but completion is MANUAL ONLY
    IF v_is_within_date_range AND v_is_within_time_window THEN
        -- Rule 1: Active - within date range AND within time window
        v_new_status := 'active';
    ELSIF v_is_within_date_range AND NOT v_is_within_time_window THEN
        -- Rule 2: Scheduled - within date range BUT outside time window
        v_new_status := 'scheduled';
    ELSE
        -- Default: Scheduled for future dates
        -- NOTE: Even if past completion time, we return 'scheduled' because completion is MANUAL ONLY
        v_new_status := 'scheduled';
    END IF;

    -- Return all debug information with explicit variable references
    RETURN QUERY SELECT
        v_shift.id::INTEGER,
        v_shift.start_date::DATE,
        v_shift.end_date::DATE,
        v_shift.start_time::TIME,
        v_shift.end_time::TIME,
        v_current_date::DATE,
        v_current_time::TIME,
        v_is_overnight::BOOLEAN,
        v_is_within_date_range::BOOLEAN,
        v_is_within_time_window::BOOLEAN,
        v_shift_end_datetime::TIMESTAMP,
        v_is_past_completion::BOOLEAN,
        v_new_status::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Add proper comment explaining the manual-only completion requirement
COMMENT ON FUNCTION debug_shift_status(INTEGER, TIMESTAMP WITH TIME ZONE) IS 
'Debug function for shift status evaluation - shows calculated status with MANUAL-ONLY completion policy. Even if past completion time, only returns active/scheduled since completion requires manual user action.';